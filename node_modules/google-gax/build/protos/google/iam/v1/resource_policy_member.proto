// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

package google.iam.v1;

import "google/api/field_behavior.proto";

option csharp_namespace = "Google.Cloud.Iam.V1";
option go_package = "cloud.google.com/go/iam/apiv1/iampb;iampb";
option php_namespace = "Google\\Cloud\\Iam\\V1";
option java_multiple_files = true;
option java_outer_classname = "ResourcePolicyMemberProto";
option java_package = "com.google.iam.v1";

// Output-only policy member strings of a Google Cloud resource's built-in
// identity.
message ResourcePolicyMember {
  // IAM policy binding member referring to a Google Cloud resource by
  // user-assigned name (https://google.aip.dev/122). If a resource is deleted
  // and recreated with the same name, the binding will be applicable to the new
  // resource.
  //
  // Example:
  // `principal://parametermanager.googleapis.com/projects/12345/name/locations/us-central1-a/parameters/my-parameter`
  string iam_policy_name_principal = 1
      [(google.api.field_behavior) = OUTPUT_ONLY];

  // IAM policy binding member referring to a Google Cloud resource by
  // system-assigned unique identifier (https://google.aip.dev/148#uid). If a
  // resource is deleted and recreated with the same name, the binding will not
  // be applicable to the new resource
  //
  // Example:
  // `principal://parametermanager.googleapis.com/projects/12345/uid/locations/us-central1-a/parameters/a918fed5`
  string iam_policy_uid_principal = 2
      [(google.api.field_behavior) = OUTPUT_ONLY];
}
