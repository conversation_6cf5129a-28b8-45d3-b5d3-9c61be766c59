# Changelog

[npm history][1]

[1]: https://www.npmjs.com/package/@google-cloud/text-to-speech?activeTab=versions

## [6.1.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v6.0.1...text-to-speech-v6.1.0) (2025-05-09)


### Features

* [texttospeech] Support HD voice custom pronunciations ([#6229](https://github.com/googleapis/google-cloud-node/issues/6229)) ([f1f9df8](https://github.com/googleapis/google-cloud-node/commit/f1f9df81259237c77433621f449c7d4325c658d8))

## [6.0.1](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v6.0.0...text-to-speech-v6.0.1) (2025-03-19)


### Bug Fixes

* [Many APIs] await/catch promises, and update listOperationsAsync return type ([#6189](https://github.com/googleapis/google-cloud-node/issues/6189)) ([0eab6d4](https://github.com/googleapis/google-cloud-node/commit/0eab6d40a12aa7f387a4621c6611aa4cbc86e178))

## [6.0.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.8.1...text-to-speech-v6.0.0) (2025-03-18)


### ⚠ BREAKING CHANGES

* upgrade to Node 18 ([#6096](https://github.com/googleapis/google-cloud-node/issues/6096))

### Features

* Add request/response debug logging to gapics, update templates to gax 5 and node 18 ([#1671](https://github.com/googleapis/google-cloud-node/issues/1671)) ([eed00f4](https://github.com/googleapis/google-cloud-node/commit/eed00f4e4de22392db3a440a20486c3eeb9d33a6))


### Bug Fixes

* Add json files to tsconfig templates ([#1692](https://github.com/googleapis/google-cloud-node/issues/1692)) (ba6be1d) ([eed00f4](https://github.com/googleapis/google-cloud-node/commit/eed00f4e4de22392db3a440a20486c3eeb9d33a6))


### Miscellaneous Chores

* Upgrade to Node 18 ([#6096](https://github.com/googleapis/google-cloud-node/issues/6096)) ([eadae64](https://github.com/googleapis/google-cloud-node/commit/eadae64d54e07aa2c65097ea52e65008d4e87436))

## [5.8.1](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.8.0...text-to-speech-v5.8.1) (2025-02-12)


### Bug Fixes

* [Many APIs] finalize fixing typings for headers in generator ([#6018](https://github.com/googleapis/google-cloud-node/issues/6018)) ([9dc5856](https://github.com/googleapis/google-cloud-node/commit/9dc585661489f51bb7a85b39519fd8b11dfffc5b))

## [5.8.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.7.0...text-to-speech-v5.8.0) (2025-01-11)


### Features

* [texttospeech] StreamingSynthesize now supports opus ([#5926](https://github.com/googleapis/google-cloud-node/issues/5926)) ([d8d7c9a](https://github.com/googleapis/google-cloud-node/commit/d8d7c9a602a787c490861d3fe386a2e651dfcae6))

## [5.7.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.6.0...text-to-speech-v5.7.0) (2024-12-18)


### Features

* [texttospeech] StreamingSynthesize now supports opus ([#5887](https://github.com/googleapis/google-cloud-node/issues/5887)) ([c335f19](https://github.com/googleapis/google-cloud-node/commit/c335f19bf4312b5552422877804bc5b032571d5a))

## [5.6.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.5.0...text-to-speech-v5.6.0) (2024-10-30)


### Features

* [texttospeech] add multi-speaker markup, which allows generating dialogue between multiple speakers ([#5760](https://github.com/googleapis/google-cloud-node/issues/5760)) ([f9dea89](https://github.com/googleapis/google-cloud-node/commit/f9dea89f81e706c13b11651936923e5506fbe5b6))

## [5.5.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.4.0...text-to-speech-v5.5.0) (2024-10-10)


### Features

* [texttospeech] Add low latency journey option to proto ([#5742](https://github.com/googleapis/google-cloud-node/issues/5742)) ([d34249c](https://github.com/googleapis/google-cloud-node/commit/d34249ca58b1738b1b46d46ac940587a9f1cca73))

## [5.4.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.3.0...text-to-speech-v5.4.0) (2024-08-19)


### Features

* [texttospeech] A new method `StreamingSynthesize` is added to service `TextToSpeech` ([#5620](https://github.com/googleapis/google-cloud-node/issues/5620)) ([a8d513d](https://github.com/googleapis/google-cloud-node/commit/a8d513d66e58983a3ac9e4726f6229bc58eb753b))

## [5.3.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.2.0...text-to-speech-v5.3.0) (2024-05-21)


### Features

* [Many APIs] update Nodejs generator to send API versions in headers for GAPICs ([#5351](https://github.com/googleapis/google-cloud-node/issues/5351)) ([01f48fc](https://github.com/googleapis/google-cloud-node/commit/01f48fce63ec4ddf801d59ee2b8c0db9f6fb8372))
* [Many APIs] update Nodejs generator to send API versions in headers for GAPICs ([#5354](https://github.com/googleapis/google-cloud-node/issues/5354)) ([a9784ed](https://github.com/googleapis/google-cloud-node/commit/a9784ed3db6ee96d171762308bbbcd57390b6866))

## [5.2.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.1.0...text-to-speech-v5.2.0) (2024-03-29)


### Features

* [Many APIs] add several fields to manage state of database encryption update ([#5191](https://github.com/googleapis/google-cloud-node/issues/5191)) ([57567db](https://github.com/googleapis/google-cloud-node/commit/57567db36033ca53ae2f54e6517b8cd12145bb82))

## [5.1.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.0.2...text-to-speech-v5.1.0) (2024-02-09)


### Features

* Trusted Private Cloud support, use the universeDomain parameter  ([#5028](https://github.com/googleapis/google-cloud-node/issues/5028)) ([852f3eb](https://github.com/googleapis/google-cloud-node/commit/852f3ebf065ee24e910580b9a1fc365acb3a744a))

## [5.0.2](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.0.1...text-to-speech-v5.0.2) (2024-01-15)


### Bug Fixes

* [texttospeech] correct long audio synthesis HTTP binding ([#4916](https://github.com/googleapis/google-cloud-node/issues/4916)) ([0be8b3e](https://github.com/googleapis/google-cloud-node/commit/0be8b3e3b2700f423eac094958d2c2e53500ff95))

## [5.0.1](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v5.0.0...text-to-speech-v5.0.1) (2023-09-06)


### Bug Fixes

* [Many APIs] simplify logic for HTTP/1.1 REST fallback option ([#4588](https://github.com/googleapis/google-cloud-node/issues/4588)) ([e5ad564](https://github.com/googleapis/google-cloud-node/commit/e5ad564f74dc7a36c0e8cd8de173428a99f1deae))

## [5.0.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.2.3...text-to-speech-v5.0.0) (2023-08-06)


### ⚠ BREAKING CHANGES

* migrate to Node 14 ([#4443](https://github.com/googleapis/google-cloud-node/issues/4443))

### Bug Fixes

* [Many APIs] fix typings for IAM methods ([#4464](https://github.com/googleapis/google-cloud-node/issues/4464)) ([c909357](https://github.com/googleapis/google-cloud-node/commit/c90935765ceee0eea6b9ce21a151707df142cf7d))


### Miscellaneous Chores

* Migrate to Node 14 ([#4443](https://github.com/googleapis/google-cloud-node/issues/4443)) ([2260f12](https://github.com/googleapis/google-cloud-node/commit/2260f12543d171bda95345e53475f5f0fdc45770))

## [4.2.3](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.2.2...text-to-speech-v4.2.3) (2023-06-28)


### Bug Fixes

* **deps:** Update dependency yargs to v17 ([#4351](https://github.com/googleapis/google-cloud-node/issues/4351)) ([4fb5285](https://github.com/googleapis/google-cloud-node/commit/4fb528559c204cee33329c4e55021aa1fd0e4974))

## [4.2.2](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.2.1...text-to-speech-v4.2.2) (2023-04-13)


### Bug Fixes

* **deps:** Bump `google-gax` to ^3.5.8 ([#4117](https://github.com/googleapis/google-cloud-node/issues/4117)) ([0b67d88](https://github.com/googleapis/google-cloud-node/commit/0b67d883963643ce1b4f6d2ccd3e8d37adf6e029))
* Minify JSON and JS files, and remove .map files ([#4143](https://github.com/googleapis/google-cloud-node/issues/4143)) ([170f7d5](https://github.com/googleapis/google-cloud-node/commit/170f7d57b8fd344d182a8e758867b8124722eebc))

## [4.2.1](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.2.0...text-to-speech-v4.2.1) (2023-02-15)


### Bug Fixes

* [Many APIs] changing format of the jsdoc links ([#3989](https://github.com/googleapis/google-cloud-node/issues/3989)) ([95399f7](https://github.com/googleapis/google-cloud-node/commit/95399f731547b06cde5ed0914d89c59fdc9fd968))

## [4.2.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.1.0...text-to-speech-v4.2.0) (2023-01-28)


### Features

* Added SuggestConversationSummary RPC ([#3853](https://github.com/googleapis/google-cloud-node/issues/3853)) ([cc352db](https://github.com/googleapis/google-cloud-node/commit/cc352db97f3bd8925bf1a7631a0ae64ff976fa4e))
* Support REST transport ([#3855](https://github.com/googleapis/google-cloud-node/issues/3855)) ([b30b329](https://github.com/googleapis/google-cloud-node/commit/b30b329f30615c0855c3fb3ae88afd1453bca18f))

## [4.1.0](https://github.com/googleapis/google-cloud-node/compare/text-to-speech-v4.0.4...text-to-speech-v4.1.0) (2022-12-16)


### Features

* Add LRS API ([#3702](https://github.com/googleapis/google-cloud-node/issues/3702)) ([21dce32](https://github.com/googleapis/google-cloud-node/commit/21dce32efc1afd7260a3fedd151d409e356e7738))
* Add LRS API ([#3706](https://github.com/googleapis/google-cloud-node/issues/3706)) ([755fd8a](https://github.com/googleapis/google-cloud-node/commit/755fd8ac98274c65c8972cd73f2869659677bc72))

## [4.0.4](https://github.com/googleapis/nodejs-text-to-speech/compare/v4.0.3...v4.0.4) (2022-11-09)


### Bug Fixes

* **deps:** Use google-gax v3.5.2 ([#641](https://github.com/googleapis/nodejs-text-to-speech/issues/641)) ([6f691f6](https://github.com/googleapis/nodejs-text-to-speech/commit/6f691f65c5d21f93117268ec87c89309fc6e667f))
* update proto definitions ([3321981](https://github.com/googleapis/nodejs-text-to-speech/commit/3321981c96b4726ef764a76bc3ce35b90943b16b))
* Update proto definitions ([#645](https://github.com/googleapis/nodejs-text-to-speech/issues/645)) ([3321981](https://github.com/googleapis/nodejs-text-to-speech/commit/3321981c96b4726ef764a76bc3ce35b90943b16b))

## [4.0.3](https://github.com/googleapis/nodejs-text-to-speech/compare/v4.0.2...v4.0.3) (2022-09-14)


### Bug Fixes

* Allow passing gax instance to client constructor ([#625](https://github.com/googleapis/nodejs-text-to-speech/issues/625)) ([df7fc10](https://github.com/googleapis/nodejs-text-to-speech/commit/df7fc1076fa70c3a41a40f18d677ea3108091e76))
* Do not import the whole google-gax from proto JS ([#1553](https://github.com/googleapis/nodejs-text-to-speech/issues/1553)) ([#624](https://github.com/googleapis/nodejs-text-to-speech/issues/624)) ([655571f](https://github.com/googleapis/nodejs-text-to-speech/commit/655571f6322adc6fd75fc05b694eddc52b7eccbe))
* Preserve default values in x-goog-request-params header ([#627](https://github.com/googleapis/nodejs-text-to-speech/issues/627)) ([26a4f84](https://github.com/googleapis/nodejs-text-to-speech/commit/26a4f8456c713272c8f3d0aab45ecbb4392e0fa1))
* use google-gax v3.3.0 ([655571f](https://github.com/googleapis/nodejs-text-to-speech/commit/655571f6322adc6fd75fc05b694eddc52b7eccbe))

## [4.0.2](https://github.com/googleapis/nodejs-text-to-speech/compare/v4.0.1...v4.0.2) (2022-08-23)


### Bug Fixes

* better support for fallback mode ([#618](https://github.com/googleapis/nodejs-text-to-speech/issues/618)) ([cece3bd](https://github.com/googleapis/nodejs-text-to-speech/commit/cece3bdb3782c3c8e8bd264fac706edc887c281b))
* change import long to require ([#620](https://github.com/googleapis/nodejs-text-to-speech/issues/620)) ([e525bd8](https://github.com/googleapis/nodejs-text-to-speech/commit/e525bd8d1b4ecf281b8b3d6145f5183384dacc3f))
* remove pip install statements ([#1546](https://github.com/googleapis/nodejs-text-to-speech/issues/1546)) ([#623](https://github.com/googleapis/nodejs-text-to-speech/issues/623)) ([6626498](https://github.com/googleapis/nodejs-text-to-speech/commit/6626498331534247b53a876b06aefa11c689b5a2))

## [4.0.1](https://github.com/googleapis/nodejs-text-to-speech/compare/v4.0.0...v4.0.1) (2022-06-30)


### Bug Fixes

* **docs:** describe fallback rest option ([#610](https://github.com/googleapis/nodejs-text-to-speech/issues/610)) ([26bc91a](https://github.com/googleapis/nodejs-text-to-speech/commit/26bc91afb45e51486061d00517c4acb0de7aaa2e))

## [4.0.0](https://github.com/googleapis/nodejs-text-to-speech/compare/v3.4.0...v4.0.0) (2022-06-10)


### ⚠ BREAKING CHANGES

* update library to use Node 12 (#604)

### Features

* Consolidate task details into service API and add orchestration result details ([74f7cfb](https://github.com/googleapis/nodejs-text-to-speech/commit/74f7cfb6947c0b9b35dcddb9ede343877b4938a6))
* promote CustomVoiceParams to v1 ([#591](https://github.com/googleapis/nodejs-text-to-speech/issues/591)) ([fe647dc](https://github.com/googleapis/nodejs-text-to-speech/commit/fe647dc8735792333e0203844279da6ada898e1a))


### Build System

* update library to use Node 12 ([#604](https://github.com/googleapis/nodejs-text-to-speech/issues/604)) ([4395d56](https://github.com/googleapis/nodejs-text-to-speech/commit/4395d5696205c257498d33693e53135da998ed38))

## [3.4.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.3.1...v3.4.0) (2021-12-09)


### Features

* adds support for MULAW and ALAW audio encoding ([#565](https://www.github.com/googleapis/nodejs-text-to-speech/issues/565)) ([cf7cb74](https://www.github.com/googleapis/nodejs-text-to-speech/commit/cf7cb74655a5bd1cfc13b670a4b620ac8dd78138))
* update v1beta1 proto ([#562](https://www.github.com/googleapis/nodejs-text-to-speech/issues/562)) ([0d78e6c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/0d78e6c87e51c040c5f35064b53f96d1353f93b9))

### [3.3.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.3.0...v3.3.1) (2021-09-10)


### Bug Fixes

* **build:** set default branch to main ([#548](https://www.github.com/googleapis/nodejs-text-to-speech/issues/548)) ([6891351](https://www.github.com/googleapis/nodejs-text-to-speech/commit/6891351d06b48e6337567c4fb9e6f885204920e3))

## [3.3.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.7...v3.3.0) (2021-08-23)


### Features

* turns on self-signed JWT feature flag ([#543](https://www.github.com/googleapis/nodejs-text-to-speech/issues/543)) ([bd5ecd3](https://www.github.com/googleapis/nodejs-text-to-speech/commit/bd5ecd31f2cf39486b85c62e1f5d35850cde22b9))

### [3.2.7](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.6...v3.2.7) (2021-08-17)


### Bug Fixes

* **deps:** google-gax v2.24.1 ([#541](https://www.github.com/googleapis/nodejs-text-to-speech/issues/541)) ([0c93b5b](https://www.github.com/googleapis/nodejs-text-to-speech/commit/0c93b5bd89bd983c1a9d658d22179b8732b533ed))

### [3.2.6](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.5...v3.2.6) (2021-07-21)


### Bug Fixes

* Updating WORKSPACE files to use the newest version of the Typescript generator. ([#534](https://www.github.com/googleapis/nodejs-text-to-speech/issues/534)) ([dee8dc2](https://www.github.com/googleapis/nodejs-text-to-speech/commit/dee8dc23024f67d5ad3888e2ea4b6829cbd4cae6))

### [3.2.5](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.4...v3.2.5) (2021-07-12)


### Bug Fixes

* **deps:** google-gax v2.17.1 ([#532](https://www.github.com/googleapis/nodejs-text-to-speech/issues/532)) ([200ddae](https://www.github.com/googleapis/nodejs-text-to-speech/commit/200ddae856e42b45a10a34d903adc699eb188c7c))

### [3.2.4](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.3...v3.2.4) (2021-06-30)


### Bug Fixes

* **deps:** google-gax v2.17.0 with mTLS ([#529](https://www.github.com/googleapis/nodejs-text-to-speech/issues/529)) ([5c6c68c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/5c6c68cb54aa684a4d2f33cedc6565ef2f17e153))

### [3.2.3](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.2...v3.2.3) (2021-06-23)


### Bug Fixes

* make request optional in all cases ([#525](https://www.github.com/googleapis/nodejs-text-to-speech/issues/525)) ([256f21b](https://www.github.com/googleapis/nodejs-text-to-speech/commit/256f21bada718586549f07327be2509c79da3912))

### [3.2.2](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.1...v3.2.2) (2021-06-04)


### Bug Fixes

* GoogleAdsError missing using generator version after 1.3.0 ([#515](https://www.github.com/googleapis/nodejs-text-to-speech/issues/515)) ([f48e238](https://www.github.com/googleapis/nodejs-text-to-speech/commit/f48e238ae8141c4bd92cfa2fbca8f6af7cc07355))

### [3.2.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.2.0...v3.2.1) (2021-05-12)


### Bug Fixes

* **deps:** require google-gax v2.12.0 ([#505](https://www.github.com/googleapis/nodejs-text-to-speech/issues/505)) ([077490c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/077490cb19e0cd3835c449392556131b3e1e9373))
* use require() to load JSON protos ([#508](https://www.github.com/googleapis/nodejs-text-to-speech/issues/508)) ([5046bb9](https://www.github.com/googleapis/nodejs-text-to-speech/commit/5046bb9e223e8effe5a9a414783e1e482c8f8b4c))

## [3.2.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.1.3...v3.2.0) (2021-04-16)


### Features

* add ALAW support on client library. And improve the ListVoiceRequest message's documentation ([#490](https://www.github.com/googleapis/nodejs-text-to-speech/issues/490)) ([0c5217c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/0c5217ce2882297aae399f83a444eaa6a7483811))

### [3.1.3](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.1.2...v3.1.3) (2020-11-25)


### Bug Fixes

* **browser:** check for fetch on window ([a0925b3](https://www.github.com/googleapis/nodejs-text-to-speech/commit/a0925b35d4e39587388cfe05685891d523f1bc49))

### [3.1.2](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.1.1...v3.1.2) (2020-11-06)


### Bug Fixes

* do not modify options object, use defaultScopes ([#463](https://www.github.com/googleapis/nodejs-text-to-speech/issues/463)) ([c3558d8](https://www.github.com/googleapis/nodejs-text-to-speech/commit/c3558d84568ed53f794f8e8136ddaf96e84630d6))

### [3.1.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.1.0...v3.1.1) (2020-09-12)


### Bug Fixes

* **deps:** update dependency yargs to v16 ([#446](https://www.github.com/googleapis/nodejs-text-to-speech/issues/446)) ([d069082](https://www.github.com/googleapis/nodejs-text-to-speech/commit/d069082f8e5630a80e3b064525dd51f011f8c0dd))

## [3.1.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.0.1...v3.1.0) (2020-07-30)


### Features

* support MULAW and MP3_64_KBPS audio encoding and support timepointing via SSML <mark> tag ([8826c87](https://www.github.com/googleapis/nodejs-text-to-speech/commit/8826c8714498d4d87546b2d61ea9ebbcff443971))

### [3.0.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v3.0.0...v3.0.1) (2020-06-29)


### Bug Fixes

* handle fallback option properly ([#413](https://www.github.com/googleapis/nodejs-text-to-speech/issues/413)) ([9ca230e](https://www.github.com/googleapis/nodejs-text-to-speech/commit/9ca230ea459b5c385ab259af943ba7efdc8437ec))
* update node issue template ([#415](https://www.github.com/googleapis/nodejs-text-to-speech/issues/415)) ([5f506f0](https://www.github.com/googleapis/nodejs-text-to-speech/commit/5f506f013f46c734fef6fbeb38ed1827f0648514))

## [3.0.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.3.0...v3.0.0) (2020-06-04)


### ⚠ BREAKING CHANGES

* The library now supports Node.js v10+. The last version to support Node.js v8 is tagged legacy-8 on NPM.

### Features

* drop node8 support ([#373](https://www.github.com/googleapis/nodejs-text-to-speech/issues/373)) ([21e3598](https://www.github.com/googleapis/nodejs-text-to-speech/commit/21e359885c39d4dfc6ed0a231fd66d35628fa0cb))


### Bug Fixes

* regen protos and tests, formatting ([#401](https://www.github.com/googleapis/nodejs-text-to-speech/issues/401)) ([6c7d38a](https://www.github.com/googleapis/nodejs-text-to-speech/commit/6c7d38a0a217d6d7a7d77dcde3756b10e47fb220))
* remove eslint, update gax, fix generated protos, run the generator ([#385](https://www.github.com/googleapis/nodejs-text-to-speech/issues/385)) ([f035747](https://www.github.com/googleapis/nodejs-text-to-speech/commit/f0357470f152c97a8203edeff2a8385068e3e316))
* remove unused files in package ([#389](https://www.github.com/googleapis/nodejs-text-to-speech/issues/389)) ([7b1e5e4](https://www.github.com/googleapis/nodejs-text-to-speech/commit/7b1e5e4abe442e54219b53a7e17ab20c0c277995))
* synth.py clean up for multiple version ([#403](https://www.github.com/googleapis/nodejs-text-to-speech/issues/403)) ([1b36546](https://www.github.com/googleapis/nodejs-text-to-speech/commit/1b36546437ec40e882d771c238a2926a5c18eee9))

## [2.3.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.2.0...v2.3.0) (2020-03-06)


### Features

* deferred client initialization ([#359](https://www.github.com/googleapis/nodejs-text-to-speech/issues/359)) ([547fb77](https://www.github.com/googleapis/nodejs-text-to-speech/commit/547fb77d625a6c25ef288e8bdad19d448113271e))
* export protos in src/index.ts ([b6ca82f](https://www.github.com/googleapis/nodejs-text-to-speech/commit/b6ca82f901d4791d3d4994a5782c7f19d449becf))

## [2.2.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.1.3...v2.2.0) (2020-02-12)


### Features

* bump release level to ga ([#342](https://www.github.com/googleapis/nodejs-text-to-speech/issues/342)) ([eca7bfa](https://www.github.com/googleapis/nodejs-text-to-speech/commit/eca7bfabbab2fc169e77922bbda3d7b79e34e1c4))

### [2.1.3](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.1.2...v2.1.3) (2020-02-01)


### Bug Fixes

* enum, bytes, and Long types now accept strings ([3839359](https://www.github.com/googleapis/nodejs-text-to-speech/commit/3839359a2cc8397f93fdb656a9b4c3a04b0ef488))

### [2.1.2](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.1.1...v2.1.2) (2020-01-03)


### Bug Fixes

* "client already closed" error fires earlier ([41b39bc](https://www.github.com/googleapis/nodejs-text-to-speech/commit/41b39bce3a598e6034a4b63c122b1ce89ddec5b5))

### [2.1.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.1.0...v2.1.1) (2019-12-30)


### Bug Fixes

* increase timeout from 20s to 60s ([#312](https://www.github.com/googleapis/nodejs-text-to-speech/issues/312)) ([ea9f75c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/ea9f75c383351724ae0f5186073c5289d2f8996a))
* move region tag to encompass catch ([#320](https://www.github.com/googleapis/nodejs-text-to-speech/issues/320)) ([988c5f5](https://www.github.com/googleapis/nodejs-text-to-speech/commit/988c5f55859bca543a79800d6eb4668dafeec70f))

## [2.1.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.0.2...v2.1.0) (2019-12-11)


### Features

* make service stub public ([921f404](https://www.github.com/googleapis/nodejs-text-to-speech/commit/921f404c156379c6073f5a0db224df703b2d3e3e))


### Bug Fixes

* **deps:** pin TypeScript below 3.7.0 ([4da93ee](https://www.github.com/googleapis/nodejs-text-to-speech/commit/4da93ee0b509c1260b9c49e7794b6546ecd2ed6e))

### [2.0.2](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.0.1...v2.0.2) (2019-11-20)


### Bug Fixes

* **docs:** bump release level to beta ([#297](https://www.github.com/googleapis/nodejs-text-to-speech/issues/297)) ([247ae7a](https://www.github.com/googleapis/nodejs-text-to-speech/commit/247ae7a1c402d04ec5285faad6e92d736a03c825))

### [2.0.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v2.0.0...v2.0.1) (2019-11-18)


### Bug Fixes

* **deps:** update dependency yargs to v15 ([#295](https://www.github.com/googleapis/nodejs-text-to-speech/issues/295)) ([2c7eea2](https://www.github.com/googleapis/nodejs-text-to-speech/commit/2c7eea213ad6211a38b0ee2d591d52d79a3b4c0a))

## [2.0.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.4.1...v2.0.0) (2019-11-14)


### ⚠ BREAKING CHANGES

* move library to typescript code generation (#285)

Starting with this release, the auto-generated library code was changed from JavaScript to TypeScript, so all the TypeScript types are now exported from the `.d.ts` files.

Please note that if you used `@types/google-cloud__text-to-speech` package to get TypeScript types for this library, you need to stop using it and remove it from your `devDependencies`.

If you see any issues caused by this migration to TypeScript, please feel free to submit an issue!

### Features

* move library to typescript code generation ([#285](https://www.github.com/googleapis/nodejs-text-to-speech/issues/285)) ([d3d6208](https://www.github.com/googleapis/nodejs-text-to-speech/commit/d3d620853adc54fe7b671fa01643f6b0ef94794b))


### Bug Fixes

* import long into proto ts declaration file ([#288](https://www.github.com/googleapis/nodejs-text-to-speech/issues/288)) ([5bfb2ab](https://www.github.com/googleapis/nodejs-text-to-speech/commit/5bfb2ab7f76625361d52ad945733087877e03800))
* **docs:** snippets are now replaced in jsdoc comments ([#287](https://www.github.com/googleapis/nodejs-text-to-speech/issues/287)) ([6e77aaf](https://www.github.com/googleapis/nodejs-text-to-speech/commit/6e77aaf10d814ab5366117c7251c29f25aa603b3))

### [1.4.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.4.0...v1.4.1) (2019-10-22)


### Bug Fixes

* **deps:** bump google-gax to 1.7.5 ([#280](https://www.github.com/googleapis/nodejs-text-to-speech/issues/280)) ([9e426ca](https://www.github.com/googleapis/nodejs-text-to-speech/commit/9e426cab4d62165d0cb79d8e489a5ccd1a1cfb46))

## [1.4.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.3.0...v1.4.0) (2019-10-09)


### Bug Fixes

* use compatible version of google-gax ([475c794](https://www.github.com/googleapis/nodejs-text-to-speech/commit/475c794))


### Features

* .d.ts for protos ([#270](https://www.github.com/googleapis/nodejs-text-to-speech/issues/270)) ([dd7f91e](https://www.github.com/googleapis/nodejs-text-to-speech/commit/dd7f91e))

## [1.3.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.2.0...v1.3.0) (2019-09-16)


### Bug Fixes

* **docs:** READMEs updated with new samples ([d3e09e0](https://www.github.com/googleapis/nodejs-text-to-speech/commit/d3e09e0))
* add proto field behavior options ([#265](https://www.github.com/googleapis/nodejs-text-to-speech/issues/265)) ([1f184a3](https://www.github.com/googleapis/nodejs-text-to-speech/commit/1f184a3))


### Features

* code samples for ssml addresses tutorial ([#257](https://www.github.com/googleapis/nodejs-text-to-speech/issues/257)) ([e423f2a](https://www.github.com/googleapis/nodejs-text-to-speech/commit/e423f2a))

## [1.2.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.1.4...v1.2.0) (2019-09-05)


### Features

* load protos from JSON, grpc-fallback support ([c9a203d](https://www.github.com/googleapis/nodejs-text-to-speech/commit/c9a203d))

### [1.1.4](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.1.3...v1.1.4) (2019-08-28)


### Bug Fixes

* **deps:** update dependency yargs to v14 ([8392e8b](https://www.github.com/googleapis/nodejs-text-to-speech/commit/8392e8b))
* use correct version for x-goog-api-client header ([6e03123](https://www.github.com/googleapis/nodejs-text-to-speech/commit/6e03123))

### [1.1.3](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.1.2...v1.1.3) (2019-08-02)


### Bug Fixes

* allow calls with no request, add JSON proto ([cb8a51b](https://www.github.com/googleapis/nodejs-text-to-speech/commit/cb8a51b))

### [1.1.2](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.1.1...v1.1.2) (2019-06-26)


### Bug Fixes

* **docs:** link to reference docs section on googleapis.dev ([#242](https://www.github.com/googleapis/nodejs-text-to-speech/issues/242)) ([f05140a](https://www.github.com/googleapis/nodejs-text-to-speech/commit/f05140a))

### [1.1.1](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.1.0...v1.1.1) (2019-06-13)


### Bug Fixes

* **docs:** move to new client docs URL ([#239](https://www.github.com/googleapis/nodejs-text-to-speech/issues/239)) ([59a89d9](https://www.github.com/googleapis/nodejs-text-to-speech/commit/59a89d9))

## [1.1.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v1.0.0...v1.1.0) (2019-06-05)


### Features

* support apiEndpoint override in client constructor ([#234](https://www.github.com/googleapis/nodejs-text-to-speech/issues/234)) ([519c36c](https://www.github.com/googleapis/nodejs-text-to-speech/commit/519c36c))

## [1.0.0](https://www.github.com/googleapis/nodejs-text-to-speech/compare/v0.5.1...v1.0.0) (2019-05-14)


### Bug Fixes

* **deps:** update dependency google-gax to v1 ([#223](https://www.github.com/googleapis/nodejs-text-to-speech/issues/223)) ([aa8bbed](https://www.github.com/googleapis/nodejs-text-to-speech/commit/aa8bbed))
* DEADLINE_EXCEEDED is idempotent ([#224](https://www.github.com/googleapis/nodejs-text-to-speech/issues/224)) ([affbbd1](https://www.github.com/googleapis/nodejs-text-to-speech/commit/affbbd1))
* DEADLINE_EXCEEDED no longer treated as idempotent and retried ([9e5baee](https://www.github.com/googleapis/nodejs-text-to-speech/commit/9e5baee))
* **deps:** update dependency google-gax to ^0.26.0 ([#212](https://www.github.com/googleapis/nodejs-text-to-speech/issues/212)) ([30ea99f](https://www.github.com/googleapis/nodejs-text-to-speech/commit/30ea99f))


### Build System

* upgrade engines field to >=8.10.0 ([#214](https://www.github.com/googleapis/nodejs-text-to-speech/issues/214)) ([d0c1639](https://www.github.com/googleapis/nodejs-text-to-speech/commit/d0c1639))


### BREAKING CHANGES

* upgrade engines field to >=8.10.0 (#214)

## v0.5.1

02-14-2019 17:36 PST

### Bug Fixes
- fix: throw on invalid credentials ([#189](https://github.com/googleapis/nodejs-text-to-speech/pull/189))

### Documentation
- docs: update sample to show method inside of async function ([#188](https://github.com/googleapis/nodejs-text-to-speech/pull/188))
- docs: update links in contrib guide ([#191](https://github.com/googleapis/nodejs-text-to-speech/pull/191))
- docs: update contributing path in README ([#184](https://github.com/googleapis/nodejs-text-to-speech/pull/184))

### Internal / Testing Changes
- build: use linkinator for docs test ([#190](https://github.com/googleapis/nodejs-text-to-speech/pull/190))
- fix(deps): update dependency yargs to v13 ([#187](https://github.com/googleapis/nodejs-text-to-speech/pull/187))
- build: create docs test npm scripts ([#186](https://github.com/googleapis/nodejs-text-to-speech/pull/186))
- build: test using @grpc/grpc-js in CI ([#185](https://github.com/googleapis/nodejs-text-to-speech/pull/185))
- chore: move CONTRIBUTING.md to root ([#183](https://github.com/googleapis/nodejs-text-to-speech/pull/183))

## v0.5.0

02-05-2019 13:22 PST

 ### New Features
- feat: add the effects_profile_id property. ([#173](https://github.com/googleapis/nodejs-text-to-speech/pull/173))

 ### Dependencies
- fix(deps): update dependency google-gax to ^0.25.0 ([#178](https://github.com/googleapis/nodejs-text-to-speech/pull/178))
- chore(deps): update dependency eslint-config-prettier to v4 ([#176](https://github.com/googleapis/nodejs-text-to-speech/pull/176))
- fix(deps): update dependency google-gax to ^0.24.0 ([#175](https://github.com/googleapis/nodejs-text-to-speech/pull/175))
- fix(deps): update dependency google-gax to ^0.23.0 ([#170](https://github.com/googleapis/nodejs-text-to-speech/pull/170))

 ### Documentation
- build: ignore googleapis.com in doc link check ([#174](https://github.com/googleapis/nodejs-text-to-speech/pull/174))
- docs: fix dead link to audio profiles ([#177](https://github.com/googleapis/nodejs-text-to-speech/pull/177))
- fix(docs): fix require stmt ([#166](https://github.com/googleapis/nodejs-text-to-speech/pull/166))
- build: check broken links in generated docs ([#165](https://github.com/googleapis/nodejs-text-to-speech/pull/165))

 ### Internal / Testing Changes
- test: add a smoke test ([#172](https://github.com/googleapis/nodejs-text-to-speech/pull/172))
- refactor: modernize the sample tests ([#164](https://github.com/googleapis/nodejs-text-to-speech/pull/164))
- chore(build): inject yoshi automation key ([#161](https://github.com/googleapis/nodejs-text-to-speech/pull/161))
- chore: update nyc and eslint configs ([#160](https://github.com/googleapis/nodejs-text-to-speech/pull/160))
- chore: fix publish.sh permission +x ([#158](https://github.com/googleapis/nodejs-text-to-speech/pull/158))
- fix(build): fix Kokoro release script ([#157](https://github.com/googleapis/nodejs-text-to-speech/pull/157))
- build: add Kokoro configs for autorelease ([#156](https://github.com/googleapis/nodejs-text-to-speech/pull/156))
- chore: always nyc report before calling codecov ([#152](https://github.com/googleapis/nodejs-text-to-speech/pull/152))

## v0.4.0

12-05-2018 16:31 PST

### Implementation Changes
- chore: require node 8 for samples ([#61](https://github.com/googleapis/nodejs-text-to-speech/pull/61))

### Dependencies
- chore: remove unused deps and cleanup ([#125](https://github.com/googleapis/nodejs-text-to-speech/pull/125))
- chore: removed async from dependency list ([#140](https://github.com/googleapis/nodejs-text-to-speech/pull/140))
- fix(deps): update dependency google-gax to ^0.22.0 ([#132](https://github.com/googleapis/nodejs-text-to-speech/pull/132))
- chore(deps): update dependency @google-cloud/nodejs-repo-tools to v3 ([#129](https://github.com/googleapis/nodejs-text-to-speech/pull/129))
- chore(deps): update dependency through2 to v3 ([#123](https://github.com/googleapis/nodejs-text-to-speech/pull/123))
- chore(deps): update dependency eslint-plugin-node to v8 ([#114](https://github.com/googleapis/nodejs-text-to-speech/pull/114))
- chore(deps): update dependency canvas to v2 ([#102](https://github.com/googleapis/nodejs-text-to-speech/pull/102))
- chore(deps): update dependency eslint-plugin-prettier to v3 ([#98](https://github.com/googleapis/nodejs-text-to-speech/pull/98))
- fix(deps): update dependency google-gax to ^0.20.0 ([#85](https://github.com/googleapis/nodejs-text-to-speech/pull/85))
- chore(deps): update dependency nyc to v13 ([#78](https://github.com/googleapis/nodejs-text-to-speech/pull/78))
- fix(deps): update dependency google-gax to ^0.19.0 ([#76](https://github.com/googleapis/nodejs-text-to-speech/pull/76))
- Update region tags to standard ([#75](https://github.com/googleapis/nodejs-text-to-speech/pull/75))
- chore(deps): update dependency eslint-config-prettier to v3 ([#73](https://github.com/googleapis/nodejs-text-to-speech/pull/73))
- chore(deps): lock file maintenance ([#68](https://github.com/googleapis/nodejs-text-to-speech/pull/68))
- chore(deps): lock file maintenance ([#67](https://github.com/googleapis/nodejs-text-to-speech/pull/67))
- fix(deps): update dependency google-gax to ^0.18.0 ([#63](https://github.com/googleapis/nodejs-text-to-speech/pull/63))
- chore(deps): lock file maintenance ([#62](https://github.com/googleapis/nodejs-text-to-speech/pull/62))
- chore(deps): lock file maintenance ([#59](https://github.com/googleapis/nodejs-text-to-speech/pull/59))
- chore(deps): update dependency eslint-plugin-node to v7 ([#57](https://github.com/googleapis/nodejs-text-to-speech/pull/57))
- chore(deps): lock file maintenance ([#56](https://github.com/googleapis/nodejs-text-to-speech/pull/56))

### Documentation
- fix(docs): bad namespaces causing 404s ([#149](https://github.com/googleapis/nodejs-text-to-speech/pull/149))
- docs: update readme badges ([#143](https://github.com/googleapis/nodejs-text-to-speech/pull/143))
- docs(samples): updated samples code to use async await ([#118](https://github.com/googleapis/nodejs-text-to-speech/pull/118))

### Internal / Testing Changes
- chore: nyc ignore build/test by default ([#148](https://github.com/googleapis/nodejs-text-to-speech/pull/148))
- chore: clean up usage of prettier and eslint ([#147](https://github.com/googleapis/nodejs-text-to-speech/pull/147))
- chore: update license file ([#145](https://github.com/googleapis/nodejs-text-to-speech/pull/145))
- fix(build): fix system key decryption ([#141](https://github.com/googleapis/nodejs-text-to-speech/pull/141))
- refactor(samples): convert sample tests from ava to mocha ([#133](https://github.com/googleapis/nodejs-text-to-speech/pull/133))
- chore: add a synth.metadata
- chore: drop contributors from multiple places ([#126](https://github.com/googleapis/nodejs-text-to-speech/pull/126))
- chore: use latest npm on Windows ([#124](https://github.com/googleapis/nodejs-text-to-speech/pull/124))
- chore: update eslintignore
- chore: update CircleCI config ([#121](https://github.com/googleapis/nodejs-text-to-speech/pull/121))
- chore: update issue templates ([#113](https://github.com/googleapis/nodejs-text-to-speech/pull/113))
- chore: remove old issue template ([#111](https://github.com/googleapis/nodejs-text-to-speech/pull/111))
- build: run tests on node11 ([#110](https://github.com/googleapis/nodejs-text-to-speech/pull/110))
- chores(build): do not collect sponge.xml from windows builds ([#109](https://github.com/googleapis/nodejs-text-to-speech/pull/109))
- chores(build): run codecov on continuous builds ([#108](https://github.com/googleapis/nodejs-text-to-speech/pull/108))
- chore: update new issue template ([#107](https://github.com/googleapis/nodejs-text-to-speech/pull/107))
- build: fix codecov uploading on Kokoro ([#101](https://github.com/googleapis/nodejs-text-to-speech/pull/101))
- Update kokoro config ([#99](https://github.com/googleapis/nodejs-text-to-speech/pull/99))
- build: prevent system/sample-test from leaking credentials
- Update the kokoro config ([#94](https://github.com/googleapis/nodejs-text-to-speech/pull/94))
- test: remove appveyor config ([#93](https://github.com/googleapis/nodejs-text-to-speech/pull/93))
- Update the CI config ([#92](https://github.com/googleapis/nodejs-text-to-speech/pull/92))
- Fix the linter ([#90](https://github.com/googleapis/nodejs-text-to-speech/pull/90))
- Enable prefer-const in the eslint config ([#88](https://github.com/googleapis/nodejs-text-to-speech/pull/88))
- Enable no-var in eslint ([#87](https://github.com/googleapis/nodejs-text-to-speech/pull/87))
- Switch to let/const ([#86](https://github.com/googleapis/nodejs-text-to-speech/pull/86))
- Update CI config ([#84](https://github.com/googleapis/nodejs-text-to-speech/pull/84))
- Update CI config ([#83](https://github.com/googleapis/nodejs-text-to-speech/pull/83))
- Retry npm install in CI ([#81](https://github.com/googleapis/nodejs-text-to-speech/pull/81))
- Add templates to synth.py and run it ([#79](https://github.com/googleapis/nodejs-text-to-speech/pull/79))
- chore: do not use npm ci ([#72](https://github.com/googleapis/nodejs-text-to-speech/pull/72))
- chore: ignore package-lock.json ([#69](https://github.com/googleapis/nodejs-text-to-speech/pull/69))
- chore: update renovate config ([#65](https://github.com/googleapis/nodejs-text-to-speech/pull/65))
- chore: remove bonus whitespace
- Code Samples demonstrating Audio Profiles ([#31](https://github.com/googleapis/nodejs-text-to-speech/pull/31))
- remove that whitespace ([#64](https://github.com/googleapis/nodejs-text-to-speech/pull/64))
- chore: move mocha options to mocha.opts ([#60](https://github.com/googleapis/nodejs-text-to-speech/pull/60))
- test: use strictEqual in tests ([#58](https://github.com/googleapis/nodejs-text-to-speech/pull/58))

## v0.3.0

### Implementation Changes
- Fixed the wrong region tag (#46)

#### Breaking Change:
- fix: drop support for node.js 4.x and 9.x (#49)

### New Features
- v1 GA is released (#29) 🎉

### Dependencies
- Update synth.py and update google-gax to 0.17.0 (#45)
- chore(deps): update dependency eslint to v5 (#42)
- fix(deps): update dependency yargs to v12 (#44)
- chore(deps): update dependency ava to ^0.25.0 (#37)
- fix(deps): update dependency yargs to v11 (#43)
- chore(deps): update dependency @google-cloud/nodejs-repo-tools to v2.3.0 (#36)

### Documentation
- fix: update linking for samples (#33)

### Internal / Testing Changes
- Configure Renovate (#30)
- refactor: drop repo-tool as an exec wrapper (#35)
