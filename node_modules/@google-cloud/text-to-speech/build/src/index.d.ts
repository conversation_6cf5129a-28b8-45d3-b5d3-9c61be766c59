import * as v1 from './v1';
import * as v1beta1 from './v1beta1';
declare const TextToSpeechClient: typeof v1.TextToSpeechClient;
type TextToSpeechClient = v1.TextToSpeechClient;
declare const TextToSpeechLongAudioSynthesizeClient: typeof v1.TextToSpeechLongAudioSynthesizeClient;
type TextToSpeechLongAudioSynthesizeClient = v1.TextToSpeechLongAudioSynthesizeClient;
export { v1, v1beta1, TextToSpeechClient, TextToSpeechLongAudioSynthesizeClient };
declare const _default: {
    v1: typeof v1;
    v1beta1: typeof v1beta1;
    TextToSpeechClient: typeof v1.TextToSpeechClient;
    TextToSpeechLongAudioSynthesizeClient: typeof v1.TextToSpeechLongAudioSynthesizeClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
