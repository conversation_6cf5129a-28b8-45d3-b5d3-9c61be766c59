"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextToSpeechClient = void 0;
const stream_1 = require("stream");
const jsonProtos = require("../../protos/protos.json");
const google_gax_1 = require("google-gax");
/**
 * Client JSON configuration object, loaded from
 * `src/v1beta1/text_to_speech_client_config.json`.
 * This file defines retry strategy and timeouts for all API methods in this library.
 */
const gapicConfig = require("./text_to_speech_client_config.json");
const version = require('../../../package.json').version;
/**
 *  Service that implements Google Cloud Text-to-Speech API.
 * @class
 * @memberof v1beta1
 */
class TextToSpeechClient {
    _terminated = false;
    _opts;
    _providedCustomServicePath;
    _gaxModule;
    _gaxGrpc;
    _protos;
    _defaults;
    _universeDomain;
    _servicePath;
    _log = google_gax_1.loggingUtils.log('text-to-speech');
    auth;
    descriptors = {
        page: {},
        stream: {},
        longrunning: {},
        batching: {},
    };
    warn;
    innerApiCalls;
    pathTemplates;
    textToSpeechStub;
    /**
     * Construct an instance of TextToSpeechClient.
     *
     * @param {object} [options] - The configuration object.
     * The options accepted by the constructor are described in detail
     * in [this document](https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#creating-the-client-instance).
     * The common options are:
     * @param {object} [options.credentials] - Credentials object.
     * @param {string} [options.credentials.client_email]
     * @param {string} [options.credentials.private_key]
     * @param {string} [options.email] - Account email address. Required when
     *     using a .pem or .p12 keyFilename.
     * @param {string} [options.keyFilename] - Full path to the a .json, .pem, or
     *     .p12 key downloaded from the Google Developers Console. If you provide
     *     a path to a JSON file, the projectId option below is not necessary.
     *     NOTE: .pem and .p12 require you to specify options.email as well.
     * @param {number} [options.port] - The port on which to connect to
     *     the remote host.
     * @param {string} [options.projectId] - The project ID from the Google
     *     Developer's Console, e.g. 'grape-spaceship-123'. We will also check
     *     the environment variable GCLOUD_PROJECT for your project ID. If your
     *     app is running in an environment which supports
     *     {@link https://cloud.google.com/docs/authentication/application-default-credentials Application Default Credentials},
     *     your project ID will be detected automatically.
     * @param {string} [options.apiEndpoint] - The domain name of the
     *     API remote host.
     * @param {gax.ClientConfig} [options.clientConfig] - Client configuration override.
     *     Follows the structure of {@link gapicConfig}.
     * @param {boolean} [options.fallback] - Use HTTP/1.1 REST mode.
     *     For more information, please check the
     *     {@link https://github.com/googleapis/gax-nodejs/blob/main/client-libraries.md#http11-rest-api-mode documentation}.
     * @param {gax} [gaxInstance]: loaded instance of `google-gax`. Useful if you
     *     need to avoid loading the default gRPC version and want to use the fallback
     *     HTTP implementation. Load only fallback version and pass it to the constructor:
     *     ```
     *     const gax = require('google-gax/build/src/fallback'); // avoids loading google-gax with gRPC
     *     const client = new TextToSpeechClient({fallback: true}, gax);
     *     ```
     */
    constructor(opts, gaxInstance) {
        // Ensure that options include all the required fields.
        const staticMembers = this.constructor;
        if (opts?.universe_domain &&
            opts?.universeDomain &&
            opts?.universe_domain !== opts?.universeDomain) {
            throw new Error('Please set either universe_domain or universeDomain, but not both.');
        }
        const universeDomainEnvVar = typeof process === 'object' && typeof process.env === 'object'
            ? process.env['GOOGLE_CLOUD_UNIVERSE_DOMAIN']
            : undefined;
        this._universeDomain =
            opts?.universeDomain ??
                opts?.universe_domain ??
                universeDomainEnvVar ??
                'googleapis.com';
        this._servicePath = 'texttospeech.' + this._universeDomain;
        const servicePath = opts?.servicePath || opts?.apiEndpoint || this._servicePath;
        this._providedCustomServicePath = !!(opts?.servicePath || opts?.apiEndpoint);
        const port = opts?.port || staticMembers.port;
        const clientConfig = opts?.clientConfig ?? {};
        const fallback = opts?.fallback ??
            (typeof window !== 'undefined' && typeof window?.fetch === 'function');
        opts = Object.assign({ servicePath, port, clientConfig, fallback }, opts);
        // Request numeric enum values if REST transport is used.
        opts.numericEnums = true;
        // If scopes are unset in options and we're connecting to a non-default endpoint, set scopes just in case.
        if (servicePath !== this._servicePath && !('scopes' in opts)) {
            opts['scopes'] = staticMembers.scopes;
        }
        // Load google-gax module synchronously if needed
        if (!gaxInstance) {
            gaxInstance = require('google-gax');
        }
        // Choose either gRPC or proto-over-HTTP implementation of google-gax.
        this._gaxModule = opts.fallback ? gaxInstance.fallback : gaxInstance;
        // Create a `gaxGrpc` object, with any grpc-specific options sent to the client.
        this._gaxGrpc = new this._gaxModule.GrpcClient(opts);
        // Save options to use in initialize() method.
        this._opts = opts;
        // Save the auth object to the client, for use by other methods.
        this.auth = this._gaxGrpc.auth;
        // Set useJWTAccessWithScope on the auth object.
        this.auth.useJWTAccessWithScope = true;
        // Set defaultServicePath on the auth object.
        this.auth.defaultServicePath = this._servicePath;
        // Set the default scopes in auth client if needed.
        if (servicePath === this._servicePath) {
            this.auth.defaultScopes = staticMembers.scopes;
        }
        // Determine the client header string.
        const clientHeader = [`gax/${this._gaxModule.version}`, `gapic/${version}`];
        if (typeof process === 'object' && 'versions' in process) {
            clientHeader.push(`gl-node/${process.versions.node}`);
        }
        else {
            clientHeader.push(`gl-web/${this._gaxModule.version}`);
        }
        if (!opts.fallback) {
            clientHeader.push(`grpc/${this._gaxGrpc.grpcVersion}`);
        }
        else {
            clientHeader.push(`rest/${this._gaxGrpc.grpcVersion}`);
        }
        if (opts.libName && opts.libVersion) {
            clientHeader.push(`${opts.libName}/${opts.libVersion}`);
        }
        // Load the applicable protos.
        this._protos = this._gaxGrpc.loadProtoJSON(jsonProtos);
        // This API contains "path templates"; forward-slash-separated
        // identifiers to uniquely identify resources within the API.
        // Create useful helper objects for these.
        this.pathTemplates = {
            modelPathTemplate: new this._gaxModule.PathTemplate('projects/{project}/locations/{location}/models/{model}'),
        };
        // Some of the methods on this service provide streaming responses.
        // Provide descriptors for these.
        this.descriptors.stream = {
            streamingSynthesize: new this._gaxModule.StreamDescriptor(this._gaxModule.StreamType.BIDI_STREAMING, !!opts.fallback, !!opts.gaxServerStreamingRetries),
        };
        // Put together the default options sent with requests.
        this._defaults = this._gaxGrpc.constructSettings('google.cloud.texttospeech.v1beta1.TextToSpeech', gapicConfig, opts.clientConfig || {}, { 'x-goog-api-client': clientHeader.join(' ') });
        // Set up a dictionary of "inner API calls"; the core implementation
        // of calling the API is handled in `google-gax`, with this code
        // merely providing the destination and request information.
        this.innerApiCalls = {};
        // Add a warn function to the client constructor so it can be easily tested.
        this.warn = this._gaxModule.warn;
    }
    /**
     * Initialize the client.
     * Performs asynchronous operations (such as authentication) and prepares the client.
     * This function will be called automatically when any class method is called for the
     * first time, but if you need to initialize it before calling an actual method,
     * feel free to call initialize() directly.
     *
     * You can await on this method if you want to make sure the client is initialized.
     *
     * @returns {Promise} A promise that resolves to an authenticated service stub.
     */
    initialize() {
        // If the client stub promise is already initialized, return immediately.
        if (this.textToSpeechStub) {
            return this.textToSpeechStub;
        }
        // Put together the "service stub" for
        // google.cloud.texttospeech.v1beta1.TextToSpeech.
        this.textToSpeechStub = this._gaxGrpc.createStub(this._opts.fallback
            ? this._protos.lookupService('google.cloud.texttospeech.v1beta1.TextToSpeech')
            : // eslint-disable-next-line @typescript-eslint/no-explicit-any
                this._protos.google.cloud.texttospeech.v1beta1.TextToSpeech, this._opts, this._providedCustomServicePath);
        // Iterate over each of the methods that the service provides
        // and create an API call method for each.
        const textToSpeechStubMethods = [
            'listVoices',
            'synthesizeSpeech',
            'streamingSynthesize',
        ];
        for (const methodName of textToSpeechStubMethods) {
            const callPromise = this.textToSpeechStub.then(stub => (...args) => {
                if (this._terminated) {
                    if (methodName in this.descriptors.stream) {
                        const stream = new stream_1.PassThrough({ objectMode: true });
                        setImmediate(() => {
                            stream.emit('error', new this._gaxModule.GoogleError('The client has already been closed.'));
                        });
                        return stream;
                    }
                    return Promise.reject('The client has already been closed.');
                }
                const func = stub[methodName];
                return func.apply(stub, args);
            }, (err) => () => {
                throw err;
            });
            const descriptor = this.descriptors.stream[methodName] || undefined;
            const apiCall = this._gaxModule.createApiCall(callPromise, this._defaults[methodName], descriptor, this._opts.fallback);
            this.innerApiCalls[methodName] = apiCall;
        }
        return this.textToSpeechStub;
    }
    /**
     * The DNS address for this API service.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get servicePath() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static servicePath is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'texttospeech.googleapis.com';
    }
    /**
     * The DNS address for this API service - same as servicePath.
     * @deprecated Use the apiEndpoint method of the client instance.
     * @returns {string} The DNS address for this service.
     */
    static get apiEndpoint() {
        if (typeof process === 'object' &&
            typeof process.emitWarning === 'function') {
            process.emitWarning('Static apiEndpoint is deprecated, please use the instance method instead.', 'DeprecationWarning');
        }
        return 'texttospeech.googleapis.com';
    }
    /**
     * The DNS address for this API service.
     * @returns {string} The DNS address for this service.
     */
    get apiEndpoint() {
        return this._servicePath;
    }
    get universeDomain() {
        return this._universeDomain;
    }
    /**
     * The port for this API service.
     * @returns {number} The default port for this service.
     */
    static get port() {
        return 443;
    }
    /**
     * The scopes needed to make gRPC calls for every method defined
     * in this service.
     * @returns {string[]} List of default scopes.
     */
    static get scopes() {
        return ['https://www.googleapis.com/auth/cloud-platform'];
    }
    /**
     * Return the project ID used by this class.
     * @returns {Promise} A promise that resolves to string containing the project ID.
     */
    getProjectId(callback) {
        if (callback) {
            this.auth.getProjectId(callback);
            return;
        }
        return this.auth.getProjectId();
    }
    listVoices(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('listVoices request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('listVoices response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .listVoices(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('listVoices response %j', response);
            return [response, options, rawResponse];
        });
    }
    synthesizeSpeech(request, optionsOrCallback, callback) {
        request = request || {};
        let options;
        if (typeof optionsOrCallback === 'function' && callback === undefined) {
            callback = optionsOrCallback;
            options = {};
        }
        else {
            options = optionsOrCallback;
        }
        options = options || {};
        options.otherArgs = options.otherArgs || {};
        options.otherArgs.headers = options.otherArgs.headers || {};
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('synthesizeSpeech request %j', request);
        const wrappedCallback = callback
            ? (error, response, options, rawResponse) => {
                this._log.info('synthesizeSpeech response %j', response);
                callback(error, response, options, rawResponse); // We verified callback above.
            }
            : undefined;
        return this.innerApiCalls
            .synthesizeSpeech(request, options, wrappedCallback)
            ?.then(([response, options, rawResponse]) => {
            this._log.info('synthesizeSpeech response %j', response);
            return [response, options, rawResponse];
        });
    }
    /**
     * Performs bidirectional streaming speech synthesis: receive audio while
     * sending text.
     *
     * @param {object} [options]
     *   Call options. See {@link https://googleapis.dev/nodejs/google-gax/latest/interfaces/CallOptions.html|CallOptions} for more details.
     * @returns {Stream}
     *   An object stream which is both readable and writable. It accepts objects
     *   representing {@link protos.google.cloud.texttospeech.v1beta1.StreamingSynthesizeRequest|StreamingSynthesizeRequest} for write() method, and
     *   will emit objects representing {@link protos.google.cloud.texttospeech.v1beta1.StreamingSynthesizeResponse|StreamingSynthesizeResponse} on 'data' event asynchronously.
     *   Please see the {@link https://github.com/googleapis/gax-nodejs/blob/master/client-libraries.md#bi-directional-streaming | documentation }
     *   for more details and examples.
     * @example <caption>include:samples/generated/v1beta1/text_to_speech.streaming_synthesize.js</caption>
     * region_tag:texttospeech_v1beta1_generated_TextToSpeech_StreamingSynthesize_async
     */
    streamingSynthesize(options) {
        this.initialize().catch(err => {
            throw err;
        });
        this._log.info('streamingSynthesize stream %j', options);
        return this.innerApiCalls.streamingSynthesize(null, options);
    }
    // --------------------
    // -- Path templates --
    // --------------------
    /**
     * Return a fully-qualified model resource name string.
     *
     * @param {string} project
     * @param {string} location
     * @param {string} model
     * @returns {string} Resource name string.
     */
    modelPath(project, location, model) {
        return this.pathTemplates.modelPathTemplate.render({
            project: project,
            location: location,
            model: model,
        });
    }
    /**
     * Parse the project from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the project.
     */
    matchProjectFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).project;
    }
    /**
     * Parse the location from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the location.
     */
    matchLocationFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).location;
    }
    /**
     * Parse the model from Model resource.
     *
     * @param {string} modelName
     *   A fully-qualified path representing Model resource.
     * @returns {string} A string representing the model.
     */
    matchModelFromModelName(modelName) {
        return this.pathTemplates.modelPathTemplate.match(modelName).model;
    }
    /**
     * Terminate the gRPC channel and close the client.
     *
     * The client will no longer be usable and all future behavior is undefined.
     * @returns {Promise} A promise that resolves when the client is closed.
     */
    close() {
        if (this.textToSpeechStub && !this._terminated) {
            return this.textToSpeechStub.then(stub => {
                this._log.info('ending gRPC channel');
                this._terminated = true;
                stub.close();
            });
        }
        return Promise.resolve();
    }
}
exports.TextToSpeechClient = TextToSpeechClient;
//# sourceMappingURL=text_to_speech_client.js.map