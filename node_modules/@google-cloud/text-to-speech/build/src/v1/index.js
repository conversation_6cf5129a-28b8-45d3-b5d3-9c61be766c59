"use strict";
// Copyright 2025 Google LLC
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     https://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//
// ** This file is automatically generated by gapic-generator-typescript. **
// ** https://github.com/googleapis/gapic-generator-typescript **
// ** All changes to this file may be overwritten. **
Object.defineProperty(exports, "__esModule", { value: true });
exports.TextToSpeechLongAudioSynthesizeClient = exports.TextToSpeechClient = void 0;
var text_to_speech_client_1 = require("./text_to_speech_client");
Object.defineProperty(exports, "TextToSpeechClient", { enumerable: true, get: function () { return text_to_speech_client_1.TextToSpeechClient; } });
var text_to_speech_long_audio_synthesize_client_1 = require("./text_to_speech_long_audio_synthesize_client");
Object.defineProperty(exports, "TextToSpeechLongAudioSynthesizeClient", { enumerable: true, get: function () { return text_to_speech_long_audio_synthesize_client_1.TextToSpeechLongAudioSynthesizeClient; } });
//# sourceMappingURL=index.js.map