{"name": "@google-cloud/speech", "description": "Cloud Speech Client Library for Node.js", "version": "7.1.0", "license": "Apache-2.0", "author": "Google Inc.", "engines": {"node": ">=18"}, "repository": {"type": "git", "directory": "packages/google-cloud-speech", "url": "https://github.com/googleapis/google-cloud-node.git"}, "main": "./build/src/index.js", "files": ["build/protos", "build/src", "AUTHORS", "LICENSE", "!build/src/**/*.map"], "keywords": ["google apis client", "google api client", "google apis", "google api", "google", "google cloud platform", "google cloud", "cloud", "google speech", "speech", "Google Cloud Speech API"], "scripts": {"clean": "gts clean", "compile": "tsc -p . && cp -r protos build/", "compile-protos": "compileProtos src", "docs": "jsdoc -c .jsdoc.js", "predocs-test": "npm run docs", "docs-test": "linkinator docs", "fix": "gts fix", "lint": "gts check", "prepare": "npm run compile-protos && npm run compile", "prelint": "cd samples; npm link ../; npm i", "postpack": "minifyProtoJson", "samples-test": "cd samples/ && npm link ../ && npm i && npm test", "system-test": "c8 mocha build/system-test", "test": "c8 mocha build/test"}, "dependencies": {"@google-cloud/common": "^6.0.0", "@types/pumpify": "^1.4.4", "google-gax": "^5.0.1-rc.0", "pumpify": "^2.0.1", "stream-events": "^1.0.5", "uuid": "^11.1.0"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^22.13.9", "@types/sinon": "^17.0.4", "c8": "^10.1.3", "codecov": "^3.8.3", "gapic-tools": "^1.0.0", "gts": "^6.0.2", "jsdoc": "^4.0.4", "jsdoc-fresh": "^3.0.0", "jsdoc-region-tag": "^3.0.0", "linkinator": "^6.1.2", "long": "^5.3.1", "mocha": "^11.1.0", "pack-n-play": "^3.0.0", "sinon": "^19.0.2", "typescript": "^5.8.2"}, "homepage": "https://github.com/googleapis/google-cloud-node/tree/main/packages/google-cloud-speech"}