{"schema": "1.0", "comment": "This file maps proto services/RPCs to the corresponding library clients/methods", "language": "typescript", "protoPackage": "google.cloud.speech.v2", "libraryPackage": "@google-cloud/speech", "services": {"Speech": {"clients": {"grpc": {"libraryClient": "SpeechClient", "rpcs": {"GetRecognizer": {"methods": ["getRecognizer"]}, "Recognize": {"methods": ["recognize"]}, "GetConfig": {"methods": ["getConfig"]}, "UpdateConfig": {"methods": ["updateConfig"]}, "GetCustomClass": {"methods": ["getCustomClass"]}, "GetPhraseSet": {"methods": ["getPhraseSet"]}, "StreamingRecognize": {"methods": ["streamingRecognize"]}, "CreateRecognizer": {"methods": ["createRecognizer"]}, "UpdateRecognizer": {"methods": ["updateRecognizer"]}, "DeleteRecognizer": {"methods": ["deleteRecognizer"]}, "UndeleteRecognizer": {"methods": ["undeleteRecognizer"]}, "BatchRecognize": {"methods": ["batchRecognize"]}, "CreateCustomClass": {"methods": ["createCustomClass"]}, "UpdateCustomClass": {"methods": ["updateCustomClass"]}, "DeleteCustomClass": {"methods": ["deleteCustomClass"]}, "UndeleteCustomClass": {"methods": ["undeleteCustomClass"]}, "CreatePhraseSet": {"methods": ["createPhraseSet"]}, "UpdatePhraseSet": {"methods": ["updatePhraseSet"]}, "DeletePhraseSet": {"methods": ["deletePhraseSet"]}, "UndeletePhraseSet": {"methods": ["undeletePhraseSet"]}, "ListRecognizers": {"methods": ["listRecognizers", "listRecognizersStream", "listRecognizersAsync"]}, "ListCustomClasses": {"methods": ["listCustomClasses", "listCustomClassesStream", "listCustomClassesAsync"]}, "ListPhraseSets": {"methods": ["listPhraseSets", "listPhraseSetsStream", "listPhraseSetsAsync"]}}}, "grpc-fallback": {"libraryClient": "SpeechClient", "rpcs": {"GetRecognizer": {"methods": ["getRecognizer"]}, "Recognize": {"methods": ["recognize"]}, "GetConfig": {"methods": ["getConfig"]}, "UpdateConfig": {"methods": ["updateConfig"]}, "GetCustomClass": {"methods": ["getCustomClass"]}, "GetPhraseSet": {"methods": ["getPhraseSet"]}, "CreateRecognizer": {"methods": ["createRecognizer"]}, "UpdateRecognizer": {"methods": ["updateRecognizer"]}, "DeleteRecognizer": {"methods": ["deleteRecognizer"]}, "UndeleteRecognizer": {"methods": ["undeleteRecognizer"]}, "BatchRecognize": {"methods": ["batchRecognize"]}, "CreateCustomClass": {"methods": ["createCustomClass"]}, "UpdateCustomClass": {"methods": ["updateCustomClass"]}, "DeleteCustomClass": {"methods": ["deleteCustomClass"]}, "UndeleteCustomClass": {"methods": ["undeleteCustomClass"]}, "CreatePhraseSet": {"methods": ["createPhraseSet"]}, "UpdatePhraseSet": {"methods": ["updatePhraseSet"]}, "DeletePhraseSet": {"methods": ["deletePhraseSet"]}, "UndeletePhraseSet": {"methods": ["undeletePhraseSet"]}, "ListRecognizers": {"methods": ["listRecognizers", "listRecognizersStream", "listRecognizersAsync"]}, "ListCustomClasses": {"methods": ["listCustomClasses", "listCustomClassesStream", "listCustomClassesAsync"]}, "ListPhraseSets": {"methods": ["listPhraseSets", "listPhraseSetsStream", "listPhraseSetsAsync"]}}}}}}}