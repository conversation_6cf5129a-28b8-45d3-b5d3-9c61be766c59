import * as v1p1beta1 from './v1p1beta1';
import * as v1 from './v1';
import * as v2 from './v2';
declare const SpeechClient: typeof v1.SpeechClient;
type SpeechClient = v1.SpeechClient;
declare const AdaptationClient: typeof v1.AdaptationClient;
type AdaptationClient = v1.AdaptationClient;
export { v1, v1p1beta1, v2, SpeechClient, AdaptationClient };
declare const _default: {
    v1: typeof v1;
    v1p1beta1: typeof v1p1beta1;
    v2: typeof v2;
    SpeechClient: typeof v1.SpeechClient;
    AdaptationClient: typeof v1.AdaptationClient;
};
export default _default;
import * as protos from '../protos/protos';
export { protos };
