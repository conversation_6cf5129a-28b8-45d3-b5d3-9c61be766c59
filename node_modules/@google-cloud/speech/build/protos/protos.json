{"nested": {"google": {"nested": {"cloud": {"nested": {"speech": {"nested": {"v1": {"options": {"cc_enable_arenas": true, "go_package": "cloud.google.com/go/speech/apiv1/speechpb;speechpb", "java_multiple_files": true, "java_outer_classname": "SpeechAdaptationProto", "java_package": "com.google.cloud.speech.v1", "objc_class_prefix": "GCS"}, "nested": {"Speech": {"options": {"(google.api.default_host)": "speech.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform"}, "methods": {"Recognize": {"requestType": "RecognizeRequest", "responseType": "RecognizeResponse", "options": {"(google.api.http).post": "/v1/speech:recognize", "(google.api.http).body": "*", "(google.api.method_signature)": "config,audio"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/speech:recognize", "body": "*"}}, {"(google.api.method_signature)": "config,audio"}]}, "LongRunningRecognize": {"requestType": "LongRunningRecognizeRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1/speech:longrunningrecognize", "(google.api.http).body": "*", "(google.api.method_signature)": "config,audio", "(google.longrunning.operation_info).response_type": "LongRunningRecognizeResponse", "(google.longrunning.operation_info).metadata_type": "LongRunningRecognizeMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/speech:longrunningrecognize", "body": "*"}}, {"(google.api.method_signature)": "config,audio"}, {"(google.longrunning.operation_info)": {"response_type": "LongRunningRecognizeResponse", "metadata_type": "LongRunningRecognizeMetadata"}}]}, "StreamingRecognize": {"requestType": "StreamingRecognizeRequest", "requestStream": true, "responseType": "StreamingRecognizeResponse", "responseStream": true}}}, "RecognizeRequest": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "audio": {"type": "RecognitionAudio", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "LongRunningRecognizeRequest": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "audio": {"type": "RecognitionAudio", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "TranscriptOutputConfig", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranscriptOutputConfig": {"oneofs": {"outputType": {"oneof": ["gcsUri"]}}, "fields": {"gcsUri": {"type": "string", "id": 1}}}, "StreamingRecognizeRequest": {"oneofs": {"streamingRequest": {"oneof": ["streamingConfig", "audioContent"]}}, "fields": {"streamingConfig": {"type": "StreamingRecognitionConfig", "id": 1}, "audioContent": {"type": "bytes", "id": 2}}}, "StreamingRecognitionConfig": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "singleUtterance": {"type": "bool", "id": 2}, "interimResults": {"type": "bool", "id": 3}, "enableVoiceActivityEvents": {"type": "bool", "id": 5}, "voiceActivityTimeout": {"type": "VoiceActivityTimeout", "id": 6}}, "nested": {"VoiceActivityTimeout": {"fields": {"speechStartTimeout": {"type": "google.protobuf.Duration", "id": 1}, "speechEndTimeout": {"type": "google.protobuf.Duration", "id": 2}}}}}, "RecognitionConfig": {"fields": {"encoding": {"type": "AudioEncoding", "id": 1}, "sampleRateHertz": {"type": "int32", "id": 2}, "audioChannelCount": {"type": "int32", "id": 7}, "enableSeparateRecognitionPerChannel": {"type": "bool", "id": 12}, "languageCode": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "alternativeLanguageCodes": {"rule": "repeated", "type": "string", "id": 18}, "maxAlternatives": {"type": "int32", "id": 4}, "profanityFilter": {"type": "bool", "id": 5}, "adaptation": {"type": "SpeechAdaptation", "id": 20}, "transcriptNormalization": {"type": "TranscriptNormalization", "id": 24, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "speechContexts": {"rule": "repeated", "type": "SpeechContext", "id": 6}, "enableWordTimeOffsets": {"type": "bool", "id": 8}, "enableWordConfidence": {"type": "bool", "id": 15}, "enableAutomaticPunctuation": {"type": "bool", "id": 11}, "enableSpokenPunctuation": {"type": "google.protobuf.BoolValue", "id": 22}, "enableSpokenEmojis": {"type": "google.protobuf.BoolValue", "id": 23}, "diarizationConfig": {"type": "SpeakerDiarizationConfig", "id": 19}, "metadata": {"type": "RecognitionMetadata", "id": 9}, "model": {"type": "string", "id": 13}, "useEnhanced": {"type": "bool", "id": 14}}, "nested": {"AudioEncoding": {"values": {"ENCODING_UNSPECIFIED": 0, "LINEAR16": 1, "FLAC": 2, "MULAW": 3, "AMR": 4, "AMR_WB": 5, "OGG_OPUS": 6, "SPEEX_WITH_HEADER_BYTE": 7, "MP3": 8, "WEBM_OPUS": 9}}}}, "SpeakerDiarizationConfig": {"fields": {"enableSpeakerDiarization": {"type": "bool", "id": 1}, "minSpeakerCount": {"type": "int32", "id": 2}, "maxSpeakerCount": {"type": "int32", "id": 3}, "speakerTag": {"type": "int32", "id": 5, "options": {"deprecated": true, "(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "RecognitionMetadata": {"options": {"deprecated": true}, "fields": {"interactionType": {"type": "InteractionType", "id": 1}, "industryNaicsCodeOfAudio": {"type": "uint32", "id": 3}, "microphoneDistance": {"type": "MicrophoneDistance", "id": 4}, "originalMediaType": {"type": "OriginalMediaType", "id": 5}, "recordingDeviceType": {"type": "RecordingDeviceType", "id": 6}, "recordingDeviceName": {"type": "string", "id": 7}, "originalMimeType": {"type": "string", "id": 8}, "audioTopic": {"type": "string", "id": 10}}, "nested": {"InteractionType": {"values": {"INTERACTION_TYPE_UNSPECIFIED": 0, "DISCUSSION": 1, "PRESENTATION": 2, "PHONE_CALL": 3, "VOICEMAIL": 4, "PROFESSIONALLY_PRODUCED": 5, "VOICE_SEARCH": 6, "VOICE_COMMAND": 7, "DICTATION": 8}}, "MicrophoneDistance": {"values": {"MICROPHONE_DISTANCE_UNSPECIFIED": 0, "NEARFIELD": 1, "MIDFIELD": 2, "FARFIELD": 3}}, "OriginalMediaType": {"values": {"ORIGINAL_MEDIA_TYPE_UNSPECIFIED": 0, "AUDIO": 1, "VIDEO": 2}}, "RecordingDeviceType": {"values": {"RECORDING_DEVICE_TYPE_UNSPECIFIED": 0, "SMARTPHONE": 1, "PC": 2, "PHONE_LINE": 3, "VEHICLE": 4, "OTHER_OUTDOOR_DEVICE": 5, "OTHER_INDOOR_DEVICE": 6}}}}, "SpeechContext": {"fields": {"phrases": {"rule": "repeated", "type": "string", "id": 1}, "boost": {"type": "float", "id": 4}}}, "RecognitionAudio": {"oneofs": {"audioSource": {"oneof": ["content", "uri"]}}, "fields": {"content": {"type": "bytes", "id": 1}, "uri": {"type": "string", "id": 2}}}, "RecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 2}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 3}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 7}, "requestId": {"type": "int64", "id": 8}}}, "LongRunningRecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 2}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 3}, "outputConfig": {"type": "TranscriptOutputConfig", "id": 6}, "outputError": {"type": "google.rpc.Status", "id": 7}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 8}, "requestId": {"type": "int64", "id": 9}}}, "LongRunningRecognizeMetadata": {"fields": {"progressPercent": {"type": "int32", "id": 1}, "startTime": {"type": "google.protobuf.Timestamp", "id": 2}, "lastUpdateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "uri": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "StreamingRecognizeResponse": {"fields": {"error": {"type": "google.rpc.Status", "id": 1}, "results": {"rule": "repeated", "type": "StreamingRecognitionResult", "id": 2}, "speechEventType": {"type": "SpeechEventType", "id": 4}, "speechEventTime": {"type": "google.protobuf.Duration", "id": 8}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 5}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 9}, "requestId": {"type": "int64", "id": 10}}, "nested": {"SpeechEventType": {"values": {"SPEECH_EVENT_UNSPECIFIED": 0, "END_OF_SINGLE_UTTERANCE": 1, "SPEECH_ACTIVITY_BEGIN": 2, "SPEECH_ACTIVITY_END": 3, "SPEECH_ACTIVITY_TIMEOUT": 4}}}}, "StreamingRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "isFinal": {"type": "bool", "id": 2}, "stability": {"type": "float", "id": 3}, "resultEndTime": {"type": "google.protobuf.Duration", "id": 4}, "channelTag": {"type": "int32", "id": 5}, "languageCode": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "channelTag": {"type": "int32", "id": 2}, "resultEndTime": {"type": "google.protobuf.Duration", "id": 4}, "languageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechRecognitionAlternative": {"fields": {"transcript": {"type": "string", "id": 1}, "confidence": {"type": "float", "id": 2}, "words": {"rule": "repeated", "type": "WordInfo", "id": 3}}}, "WordInfo": {"fields": {"startTime": {"type": "google.protobuf.Duration", "id": 1}, "endTime": {"type": "google.protobuf.Duration", "id": 2}, "word": {"type": "string", "id": 3}, "confidence": {"type": "float", "id": 4}, "speakerTag": {"type": "int32", "id": 5, "options": {"deprecated": true, "(google.api.field_behavior)": "OUTPUT_ONLY"}}, "speakerLabel": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechAdaptationInfo": {"fields": {"adaptationTimeout": {"type": "bool", "id": 1}, "timeoutMessage": {"type": "string", "id": 4}}}, "CustomClass": {"options": {"(google.api.resource).type": "speech.googleapis.com/CustomClass", "(google.api.resource).pattern": "projects/{project}/locations/{location}/customClasses/{custom_class}"}, "fields": {"name": {"type": "string", "id": 1}, "customClassId": {"type": "string", "id": 2}, "items": {"rule": "repeated", "type": "ClassItem", "id": 3}}, "nested": {"ClassItem": {"fields": {"value": {"type": "string", "id": 1}}}}}, "PhraseSet": {"options": {"(google.api.resource).type": "speech.googleapis.com/PhraseSet", "(google.api.resource).pattern": "projects/{project}/locations/{location}/phraseSets/{phrase_set}"}, "fields": {"name": {"type": "string", "id": 1}, "phrases": {"rule": "repeated", "type": "Phrase", "id": 2}, "boost": {"type": "float", "id": 4}}, "nested": {"Phrase": {"fields": {"value": {"type": "string", "id": 1}, "boost": {"type": "float", "id": 2}}}}}, "SpeechAdaptation": {"fields": {"phraseSets": {"rule": "repeated", "type": "PhraseSet", "id": 1}, "phraseSetReferences": {"rule": "repeated", "type": "string", "id": 2, "options": {"(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}, "customClasses": {"rule": "repeated", "type": "CustomClass", "id": 3}, "abnfGrammar": {"type": "ABNFGrammar", "id": 4}}, "nested": {"ABNFGrammar": {"fields": {"abnfStrings": {"rule": "repeated", "type": "string", "id": 1}}}}}, "TranscriptNormalization": {"fields": {"entries": {"rule": "repeated", "type": "Entry", "id": 1}}, "nested": {"Entry": {"fields": {"search": {"type": "string", "id": 1}, "replace": {"type": "string", "id": 2}, "caseSensitive": {"type": "bool", "id": 3}}}}}, "Adaptation": {"options": {"(google.api.default_host)": "speech.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform"}, "methods": {"CreatePhraseSet": {"requestType": "CreatePhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).post": "/v1/{parent=projects/*/locations/*}/phraseSets", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,phrase_set,phrase_set_id"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*/locations/*}/phraseSets", "body": "*"}}, {"(google.api.method_signature)": "parent,phrase_set,phrase_set_id"}]}, "GetPhraseSet": {"requestType": "GetPhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).get": "/v1/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListPhraseSet": {"requestType": "ListPhraseSetRequest", "responseType": "ListPhraseSetResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/locations/*}/phraseSets", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/locations/*}/phraseSets"}}, {"(google.api.method_signature)": "parent"}]}, "UpdatePhraseSet": {"requestType": "UpdatePhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).patch": "/v1/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "(google.api.http).body": "phrase_set", "(google.api.method_signature)": "phrase_set,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "body": "phrase_set"}}, {"(google.api.method_signature)": "phrase_set,update_mask"}]}, "DeletePhraseSet": {"requestType": "DeletePhraseSetRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}]}, "CreateCustomClass": {"requestType": "CreateCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).post": "/v1/{parent=projects/*/locations/*}/customClasses", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,custom_class,custom_class_id"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{parent=projects/*/locations/*}/customClasses", "body": "*"}}, {"(google.api.method_signature)": "parent,custom_class,custom_class_id"}]}, "GetCustomClass": {"requestType": "GetCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).get": "/v1/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListCustomClasses": {"requestType": "ListCustomClassesRequest", "responseType": "ListCustomClassesResponse", "options": {"(google.api.http).get": "/v1/{parent=projects/*/locations/*}/customClasses", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{parent=projects/*/locations/*}/customClasses"}}, {"(google.api.method_signature)": "parent"}]}, "UpdateCustomClass": {"requestType": "UpdateCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).patch": "/v1/{custom_class.name=projects/*/locations/*/customClasses/*}", "(google.api.http).body": "custom_class", "(google.api.method_signature)": "custom_class,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1/{custom_class.name=projects/*/locations/*/customClasses/*}", "body": "custom_class"}}, {"(google.api.method_signature)": "custom_class,update_mask"}]}, "DeleteCustomClass": {"requestType": "DeleteCustomClassRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}]}}}, "CreatePhraseSetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/PhraseSet"}}, "phraseSetId": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "phraseSet": {"type": "PhraseSet", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdatePhraseSetRequest": {"fields": {"phraseSet": {"type": "PhraseSet", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetPhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}}}, "ListPhraseSetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/PhraseSet"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListPhraseSetResponse": {"fields": {"phraseSets": {"rule": "repeated", "type": "PhraseSet", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "DeletePhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}}}, "CreateCustomClassRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/CustomClass"}}, "customClassId": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "customClass": {"type": "CustomClass", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdateCustomClassRequest": {"fields": {"customClass": {"type": "CustomClass", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}}}, "ListCustomClassesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/CustomClass"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListCustomClassesResponse": {"fields": {"customClasses": {"rule": "repeated", "type": "CustomClass", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "DeleteCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}}}}}, "v1p1beta1": {"options": {"go_package": "cloud.google.com/go/speech/apiv1p1beta1/speechpb;speechpb", "java_multiple_files": true, "java_outer_classname": "SpeechAdaptationProto", "java_package": "com.google.cloud.speech.v1p1beta1", "objc_class_prefix": "GCS", "(google.api.resource_definition).type": "cloudkms.googleapis.com/CryptoKeyVersion", "(google.api.resource_definition).pattern": "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"}, "nested": {"Speech": {"options": {"(google.api.default_host)": "speech.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform"}, "methods": {"Recognize": {"requestType": "RecognizeRequest", "responseType": "RecognizeResponse", "options": {"(google.api.http).post": "/v1p1beta1/speech:recognize", "(google.api.http).body": "*", "(google.api.method_signature)": "config,audio"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1p1beta1/speech:recognize", "body": "*"}}, {"(google.api.method_signature)": "config,audio"}]}, "LongRunningRecognize": {"requestType": "LongRunningRecognizeRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v1p1beta1/speech:longrunningrecognize", "(google.api.http).body": "*", "(google.api.method_signature)": "config,audio", "(google.longrunning.operation_info).response_type": "LongRunningRecognizeResponse", "(google.longrunning.operation_info).metadata_type": "LongRunningRecognizeMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1p1beta1/speech:longrunningrecognize", "body": "*"}}, {"(google.api.method_signature)": "config,audio"}, {"(google.longrunning.operation_info)": {"response_type": "LongRunningRecognizeResponse", "metadata_type": "LongRunningRecognizeMetadata"}}]}, "StreamingRecognize": {"requestType": "StreamingRecognizeRequest", "requestStream": true, "responseType": "StreamingRecognizeResponse", "responseStream": true}}}, "RecognizeRequest": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "audio": {"type": "RecognitionAudio", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "LongRunningRecognizeRequest": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "audio": {"type": "RecognitionAudio", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "outputConfig": {"type": "TranscriptOutputConfig", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "TranscriptOutputConfig": {"oneofs": {"outputType": {"oneof": ["gcsUri"]}}, "fields": {"gcsUri": {"type": "string", "id": 1}}}, "StreamingRecognizeRequest": {"oneofs": {"streamingRequest": {"oneof": ["streamingConfig", "audioContent"]}}, "fields": {"streamingConfig": {"type": "StreamingRecognitionConfig", "id": 1}, "audioContent": {"type": "bytes", "id": 2}}}, "StreamingRecognitionConfig": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "singleUtterance": {"type": "bool", "id": 2}, "interimResults": {"type": "bool", "id": 3}, "enableVoiceActivityEvents": {"type": "bool", "id": 5}, "voiceActivityTimeout": {"type": "VoiceActivityTimeout", "id": 6}}, "nested": {"VoiceActivityTimeout": {"fields": {"speechStartTimeout": {"type": "google.protobuf.Duration", "id": 1}, "speechEndTimeout": {"type": "google.protobuf.Duration", "id": 2}}}}}, "RecognitionConfig": {"fields": {"encoding": {"type": "AudioEncoding", "id": 1}, "sampleRateHertz": {"type": "int32", "id": 2}, "audioChannelCount": {"type": "int32", "id": 7}, "enableSeparateRecognitionPerChannel": {"type": "bool", "id": 12}, "languageCode": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "alternativeLanguageCodes": {"rule": "repeated", "type": "string", "id": 18}, "maxAlternatives": {"type": "int32", "id": 4}, "profanityFilter": {"type": "bool", "id": 5}, "adaptation": {"type": "SpeechAdaptation", "id": 20}, "transcriptNormalization": {"type": "TranscriptNormalization", "id": 24, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "speechContexts": {"rule": "repeated", "type": "SpeechContext", "id": 6}, "enableWordTimeOffsets": {"type": "bool", "id": 8}, "enableWordConfidence": {"type": "bool", "id": 15}, "enableAutomaticPunctuation": {"type": "bool", "id": 11}, "enableSpokenPunctuation": {"type": "google.protobuf.BoolValue", "id": 22}, "enableSpokenEmojis": {"type": "google.protobuf.BoolValue", "id": 23}, "enableSpeakerDiarization": {"type": "bool", "id": 16, "options": {"deprecated": true}}, "diarizationSpeakerCount": {"type": "int32", "id": 17, "options": {"deprecated": true}}, "diarizationConfig": {"type": "SpeakerDiarizationConfig", "id": 19}, "metadata": {"type": "RecognitionMetadata", "id": 9}, "model": {"type": "string", "id": 13}, "useEnhanced": {"type": "bool", "id": 14}}, "nested": {"AudioEncoding": {"values": {"ENCODING_UNSPECIFIED": 0, "LINEAR16": 1, "FLAC": 2, "MULAW": 3, "AMR": 4, "AMR_WB": 5, "OGG_OPUS": 6, "SPEEX_WITH_HEADER_BYTE": 7, "MP3": 8, "WEBM_OPUS": 9, "ALAW": 10}}}}, "SpeakerDiarizationConfig": {"fields": {"enableSpeakerDiarization": {"type": "bool", "id": 1}, "minSpeakerCount": {"type": "int32", "id": 2}, "maxSpeakerCount": {"type": "int32", "id": 3}, "speakerTag": {"type": "int32", "id": 5, "options": {"deprecated": true, "(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "RecognitionMetadata": {"options": {"deprecated": true}, "fields": {"interactionType": {"type": "InteractionType", "id": 1}, "industryNaicsCodeOfAudio": {"type": "uint32", "id": 3}, "microphoneDistance": {"type": "MicrophoneDistance", "id": 4}, "originalMediaType": {"type": "OriginalMediaType", "id": 5}, "recordingDeviceType": {"type": "RecordingDeviceType", "id": 6}, "recordingDeviceName": {"type": "string", "id": 7}, "originalMimeType": {"type": "string", "id": 8}, "obfuscatedId": {"type": "int64", "id": 9, "options": {"deprecated": true}}, "audioTopic": {"type": "string", "id": 10}}, "nested": {"InteractionType": {"values": {"INTERACTION_TYPE_UNSPECIFIED": 0, "DISCUSSION": 1, "PRESENTATION": 2, "PHONE_CALL": 3, "VOICEMAIL": 4, "PROFESSIONALLY_PRODUCED": 5, "VOICE_SEARCH": 6, "VOICE_COMMAND": 7, "DICTATION": 8}}, "MicrophoneDistance": {"values": {"MICROPHONE_DISTANCE_UNSPECIFIED": 0, "NEARFIELD": 1, "MIDFIELD": 2, "FARFIELD": 3}}, "OriginalMediaType": {"values": {"ORIGINAL_MEDIA_TYPE_UNSPECIFIED": 0, "AUDIO": 1, "VIDEO": 2}}, "RecordingDeviceType": {"values": {"RECORDING_DEVICE_TYPE_UNSPECIFIED": 0, "SMARTPHONE": 1, "PC": 2, "PHONE_LINE": 3, "VEHICLE": 4, "OTHER_OUTDOOR_DEVICE": 5, "OTHER_INDOOR_DEVICE": 6}}}}, "SpeechContext": {"fields": {"phrases": {"rule": "repeated", "type": "string", "id": 1}, "boost": {"type": "float", "id": 4}}}, "RecognitionAudio": {"oneofs": {"audioSource": {"oneof": ["content", "uri"]}}, "fields": {"content": {"type": "bytes", "id": 1}, "uri": {"type": "string", "id": 2}}}, "RecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 2}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 3}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 7}, "requestId": {"type": "int64", "id": 8}, "usingLegacyModels": {"type": "bool", "id": 9}}}, "LongRunningRecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 2}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 3}, "outputConfig": {"type": "TranscriptOutputConfig", "id": 6}, "outputError": {"type": "google.rpc.Status", "id": 7}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 8}, "requestId": {"type": "int64", "id": 9}}}, "LongRunningRecognizeMetadata": {"fields": {"progressPercent": {"type": "int32", "id": 1}, "startTime": {"type": "google.protobuf.Timestamp", "id": 2}, "lastUpdateTime": {"type": "google.protobuf.Timestamp", "id": 3}, "uri": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "outputConfig": {"type": "TranscriptOutputConfig", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "StreamingRecognizeResponse": {"fields": {"error": {"type": "google.rpc.Status", "id": 1}, "results": {"rule": "repeated", "type": "StreamingRecognitionResult", "id": 2}, "speechEventType": {"type": "SpeechEventType", "id": 4}, "speechEventTime": {"type": "google.protobuf.Duration", "id": 8}, "totalBilledTime": {"type": "google.protobuf.Duration", "id": 5}, "speechAdaptationInfo": {"type": "SpeechAdaptationInfo", "id": 9}, "requestId": {"type": "int64", "id": 10}}, "nested": {"SpeechEventType": {"values": {"SPEECH_EVENT_UNSPECIFIED": 0, "END_OF_SINGLE_UTTERANCE": 1, "SPEECH_ACTIVITY_BEGIN": 2, "SPEECH_ACTIVITY_END": 3, "SPEECH_ACTIVITY_TIMEOUT": 4}}}}, "StreamingRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "isFinal": {"type": "bool", "id": 2}, "stability": {"type": "float", "id": 3}, "resultEndTime": {"type": "google.protobuf.Duration", "id": 4}, "channelTag": {"type": "int32", "id": 5}, "languageCode": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "channelTag": {"type": "int32", "id": 2}, "resultEndTime": {"type": "google.protobuf.Duration", "id": 4}, "languageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechRecognitionAlternative": {"fields": {"transcript": {"type": "string", "id": 1}, "confidence": {"type": "float", "id": 2}, "words": {"rule": "repeated", "type": "WordInfo", "id": 3}}}, "WordInfo": {"fields": {"startTime": {"type": "google.protobuf.Duration", "id": 1}, "endTime": {"type": "google.protobuf.Duration", "id": 2}, "word": {"type": "string", "id": 3}, "confidence": {"type": "float", "id": 4}, "speakerTag": {"type": "int32", "id": 5, "options": {"deprecated": true, "(google.api.field_behavior)": "OUTPUT_ONLY"}}, "speakerLabel": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "SpeechAdaptationInfo": {"fields": {"adaptationTimeout": {"type": "bool", "id": 1}, "timeoutMessage": {"type": "string", "id": 4}}}, "CustomClass": {"options": {"(google.api.resource).type": "speech.googleapis.com/CustomClass", "(google.api.resource).pattern": "projects/{project}/locations/{location}/customClasses/{custom_class}"}, "fields": {"name": {"type": "string", "id": 1}, "customClassId": {"type": "string", "id": 2}, "items": {"rule": "repeated", "type": "ClassItem", "id": 3}, "kmsKeyName": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}, "uid": {"type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "displayName": {"type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "state": {"type": "State", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 11, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "annotations": {"keyType": "string", "type": "string", "id": 13, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "etag": {"type": "string", "id": 14, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "reconciling": {"type": "bool", "id": 15, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}, "nested": {"ClassItem": {"fields": {"value": {"type": "string", "id": 1}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "ACTIVE": 2, "DELETED": 4}}}}, "PhraseSet": {"options": {"(google.api.resource).type": "speech.googleapis.com/PhraseSet", "(google.api.resource).pattern": "projects/{project}/locations/{location}/phraseSets/{phrase_set}"}, "fields": {"name": {"type": "string", "id": 1}, "phrases": {"rule": "repeated", "type": "Phrase", "id": 2}, "boost": {"type": "float", "id": 4}, "kmsKeyName": {"type": "string", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}, "uid": {"type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "displayName": {"type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "state": {"type": "State", "id": 11, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 13, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "annotations": {"keyType": "string", "type": "string", "id": 14, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "etag": {"type": "string", "id": 15, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "reconciling": {"type": "bool", "id": 16, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}, "nested": {"Phrase": {"fields": {"value": {"type": "string", "id": 1}, "boost": {"type": "float", "id": 2}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "ACTIVE": 2, "DELETED": 4}}}}, "SpeechAdaptation": {"fields": {"phraseSets": {"rule": "repeated", "type": "PhraseSet", "id": 1}, "phraseSetReferences": {"rule": "repeated", "type": "string", "id": 2, "options": {"(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}, "customClasses": {"rule": "repeated", "type": "CustomClass", "id": 3}, "abnfGrammar": {"type": "ABNFGrammar", "id": 4}}, "nested": {"ABNFGrammar": {"fields": {"abnfStrings": {"rule": "repeated", "type": "string", "id": 1}}}}}, "TranscriptNormalization": {"fields": {"entries": {"rule": "repeated", "type": "Entry", "id": 1}}, "nested": {"Entry": {"fields": {"search": {"type": "string", "id": 1}, "replace": {"type": "string", "id": 2}, "caseSensitive": {"type": "bool", "id": 3}}}}}, "Adaptation": {"options": {"(google.api.default_host)": "speech.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform"}, "methods": {"CreatePhraseSet": {"requestType": "CreatePhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).post": "/v1p1beta1/{parent=projects/*/locations/*}/phraseSets", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,phrase_set,phrase_set_id"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1p1beta1/{parent=projects/*/locations/*}/phraseSets", "body": "*"}}, {"(google.api.method_signature)": "parent,phrase_set,phrase_set_id"}]}, "GetPhraseSet": {"requestType": "GetPhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).get": "/v1p1beta1/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1p1beta1/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListPhraseSet": {"requestType": "ListPhraseSetRequest", "responseType": "ListPhraseSetResponse", "options": {"(google.api.http).get": "/v1p1beta1/{parent=projects/*/locations/*}/phraseSets", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1p1beta1/{parent=projects/*/locations/*}/phraseSets"}}, {"(google.api.method_signature)": "parent"}]}, "UpdatePhraseSet": {"requestType": "UpdatePhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).patch": "/v1p1beta1/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "(google.api.http).body": "phrase_set", "(google.api.method_signature)": "phrase_set,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1p1beta1/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "body": "phrase_set"}}, {"(google.api.method_signature)": "phrase_set,update_mask"}]}, "DeletePhraseSet": {"requestType": "DeletePhraseSetRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1p1beta1/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1p1beta1/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}]}, "CreateCustomClass": {"requestType": "CreateCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).post": "/v1p1beta1/{parent=projects/*/locations/*}/customClasses", "(google.api.http).body": "*", "(google.api.method_signature)": "parent,custom_class,custom_class_id"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1p1beta1/{parent=projects/*/locations/*}/customClasses", "body": "*"}}, {"(google.api.method_signature)": "parent,custom_class,custom_class_id"}]}, "GetCustomClass": {"requestType": "GetCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).get": "/v1p1beta1/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1p1beta1/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}]}, "ListCustomClasses": {"requestType": "ListCustomClassesRequest", "responseType": "ListCustomClassesResponse", "options": {"(google.api.http).get": "/v1p1beta1/{parent=projects/*/locations/*}/customClasses", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1p1beta1/{parent=projects/*/locations/*}/customClasses"}}, {"(google.api.method_signature)": "parent"}]}, "UpdateCustomClass": {"requestType": "UpdateCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).patch": "/v1p1beta1/{custom_class.name=projects/*/locations/*/customClasses/*}", "(google.api.http).body": "custom_class", "(google.api.method_signature)": "custom_class,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v1p1beta1/{custom_class.name=projects/*/locations/*/customClasses/*}", "body": "custom_class"}}, {"(google.api.method_signature)": "custom_class,update_mask"}]}, "DeleteCustomClass": {"requestType": "DeleteCustomClassRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1p1beta1/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1p1beta1/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}]}}}, "CreatePhraseSetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/PhraseSet"}}, "phraseSetId": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "phraseSet": {"type": "PhraseSet", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdatePhraseSetRequest": {"fields": {"phraseSet": {"type": "PhraseSet", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetPhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}}}, "ListPhraseSetRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/PhraseSet"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListPhraseSetResponse": {"fields": {"phraseSets": {"rule": "repeated", "type": "PhraseSet", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "DeletePhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}}}, "CreateCustomClassRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/CustomClass"}}, "customClassId": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "customClass": {"type": "CustomClass", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "UpdateCustomClassRequest": {"fields": {"customClass": {"type": "CustomClass", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "GetCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}}}, "ListCustomClassesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/CustomClass"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListCustomClassesResponse": {"fields": {"customClasses": {"rule": "repeated", "type": "CustomClass", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "DeleteCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}}}}}, "v2": {"options": {"go_package": "cloud.google.com/go/speech/apiv2/speechpb;speechpb", "java_multiple_files": true, "java_outer_classname": "LocationsMetadataProto", "java_package": "com.google.cloud.speech.v2", "(google.api.resource_definition).type": "cloudkms.googleapis.com/CryptoKeyVersion", "(google.api.resource_definition).pattern": "projects/{project}/locations/{location}/keyRings/{key_ring}/cryptoKeys/{crypto_key}/cryptoKeyVersions/{crypto_key_version}"}, "nested": {"Speech": {"options": {"(google.api.default_host)": "speech.googleapis.com", "(google.api.oauth_scopes)": "https://www.googleapis.com/auth/cloud-platform"}, "methods": {"CreateRecognizer": {"requestType": "CreateRecognizerRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{parent=projects/*/locations/*}/recognizers", "(google.api.http).body": "recognizer", "(google.api.method_signature)": "parent,recognizer,recognizer_id", "(google.longrunning.operation_info).response_type": "Recognizer", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{parent=projects/*/locations/*}/recognizers", "body": "recognizer"}}, {"(google.api.method_signature)": "parent,recognizer,recognizer_id"}, {"(google.longrunning.operation_info)": {"response_type": "Recognizer", "metadata_type": "OperationMetadata"}}]}, "ListRecognizers": {"requestType": "ListRecognizersRequest", "responseType": "ListRecognizersResponse", "options": {"(google.api.http).get": "/v2/{parent=projects/*/locations/*}/recognizers", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{parent=projects/*/locations/*}/recognizers"}}, {"(google.api.method_signature)": "parent"}]}, "GetRecognizer": {"requestType": "GetRecognizerRequest", "responseType": "Recognizer", "options": {"(google.api.http).get": "/v2/{name=projects/*/locations/*/recognizers/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{name=projects/*/locations/*/recognizers/*}"}}, {"(google.api.method_signature)": "name"}]}, "UpdateRecognizer": {"requestType": "UpdateRecognizerRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v2/{recognizer.name=projects/*/locations/*/recognizers/*}", "(google.api.http).body": "recognizer", "(google.api.method_signature)": "recognizer,update_mask", "(google.longrunning.operation_info).response_type": "Recognizer", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v2/{recognizer.name=projects/*/locations/*/recognizers/*}", "body": "recognizer"}}, {"(google.api.method_signature)": "recognizer,update_mask"}, {"(google.longrunning.operation_info)": {"response_type": "Recognizer", "metadata_type": "OperationMetadata"}}]}, "DeleteRecognizer": {"requestType": "DeleteRecognizerRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v2/{name=projects/*/locations/*/recognizers/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "Recognizer", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v2/{name=projects/*/locations/*/recognizers/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "Recognizer", "metadata_type": "OperationMetadata"}}]}, "UndeleteRecognizer": {"requestType": "UndeleteRecognizerRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{name=projects/*/locations/*/recognizers/*}:undelete", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "Recognizer", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{name=projects/*/locations/*/recognizers/*}:undelete", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "Recognizer", "metadata_type": "OperationMetadata"}}]}, "Recognize": {"requestType": "RecognizeRequest", "responseType": "RecognizeResponse", "options": {"(google.api.http).post": "/v2/{recognizer=projects/*/locations/*/recognizers/*}:recognize", "(google.api.http).body": "*", "(google.api.method_signature)": "recognizer,config,config_mask,uri"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{recognizer=projects/*/locations/*/recognizers/*}:recognize", "body": "*"}}, {"(google.api.method_signature)": "recognizer,config,config_mask,content"}, {"(google.api.method_signature)": "recognizer,config,config_mask,uri"}]}, "StreamingRecognize": {"requestType": "StreamingRecognizeRequest", "requestStream": true, "responseType": "StreamingRecognizeResponse", "responseStream": true}, "BatchRecognize": {"requestType": "BatchRecognizeRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{recognizer=projects/*/locations/*/recognizers/*}:batchRecognize", "(google.api.http).body": "*", "(google.api.method_signature)": "recognizer,config,config_mask,files", "(google.longrunning.operation_info).response_type": "BatchRecognizeResponse", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{recognizer=projects/*/locations/*/recognizers/*}:batchRecognize", "body": "*"}}, {"(google.api.method_signature)": "recognizer,config,config_mask,files"}, {"(google.longrunning.operation_info)": {"response_type": "BatchRecognizeResponse", "metadata_type": "OperationMetadata"}}]}, "GetConfig": {"requestType": "GetConfigRequest", "responseType": "Config", "options": {"(google.api.http).get": "/v2/{name=projects/*/locations/*/config}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{name=projects/*/locations/*/config}"}}, {"(google.api.method_signature)": "name"}]}, "UpdateConfig": {"requestType": "UpdateConfigRequest", "responseType": "Config", "options": {"(google.api.http).patch": "/v2/{config.name=projects/*/locations/*/config}", "(google.api.http).body": "config", "(google.api.method_signature)": "config,update_mask"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v2/{config.name=projects/*/locations/*/config}", "body": "config"}}, {"(google.api.method_signature)": "config,update_mask"}]}, "CreateCustomClass": {"requestType": "CreateCustomClassRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{parent=projects/*/locations/*}/customClasses", "(google.api.http).body": "custom_class", "(google.api.method_signature)": "parent,custom_class,custom_class_id", "(google.longrunning.operation_info).response_type": "CustomClass", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{parent=projects/*/locations/*}/customClasses", "body": "custom_class"}}, {"(google.api.method_signature)": "parent,custom_class,custom_class_id"}, {"(google.longrunning.operation_info)": {"response_type": "CustomClass", "metadata_type": "OperationMetadata"}}]}, "ListCustomClasses": {"requestType": "ListCustomClassesRequest", "responseType": "ListCustomClassesResponse", "options": {"(google.api.http).get": "/v2/{parent=projects/*/locations/*}/customClasses", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{parent=projects/*/locations/*}/customClasses"}}, {"(google.api.method_signature)": "parent"}]}, "GetCustomClass": {"requestType": "GetCustomClassRequest", "responseType": "CustomClass", "options": {"(google.api.http).get": "/v2/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}]}, "UpdateCustomClass": {"requestType": "UpdateCustomClassRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v2/{custom_class.name=projects/*/locations/*/customClasses/*}", "(google.api.http).body": "custom_class", "(google.api.method_signature)": "custom_class,update_mask", "(google.longrunning.operation_info).response_type": "CustomClass", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v2/{custom_class.name=projects/*/locations/*/customClasses/*}", "body": "custom_class"}}, {"(google.api.method_signature)": "custom_class,update_mask"}, {"(google.longrunning.operation_info)": {"response_type": "CustomClass", "metadata_type": "OperationMetadata"}}]}, "DeleteCustomClass": {"requestType": "DeleteCustomClassRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v2/{name=projects/*/locations/*/customClasses/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "CustomClass", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v2/{name=projects/*/locations/*/customClasses/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "CustomClass", "metadata_type": "OperationMetadata"}}]}, "UndeleteCustomClass": {"requestType": "UndeleteCustomClassRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{name=projects/*/locations/*/customClasses/*}:undelete", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "CustomClass", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{name=projects/*/locations/*/customClasses/*}:undelete", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "CustomClass", "metadata_type": "OperationMetadata"}}]}, "CreatePhraseSet": {"requestType": "CreatePhraseSetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{parent=projects/*/locations/*}/phraseSets", "(google.api.http).body": "phrase_set", "(google.api.method_signature)": "parent,phrase_set,phrase_set_id", "(google.longrunning.operation_info).response_type": "PhraseSet", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{parent=projects/*/locations/*}/phraseSets", "body": "phrase_set"}}, {"(google.api.method_signature)": "parent,phrase_set,phrase_set_id"}, {"(google.longrunning.operation_info)": {"response_type": "PhraseSet", "metadata_type": "OperationMetadata"}}]}, "ListPhraseSets": {"requestType": "ListPhraseSetsRequest", "responseType": "ListPhraseSetsResponse", "options": {"(google.api.http).get": "/v2/{parent=projects/*/locations/*}/phraseSets", "(google.api.method_signature)": "parent"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{parent=projects/*/locations/*}/phraseSets"}}, {"(google.api.method_signature)": "parent"}]}, "GetPhraseSet": {"requestType": "GetPhraseSetRequest", "responseType": "PhraseSet", "options": {"(google.api.http).get": "/v2/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v2/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}]}, "UpdatePhraseSet": {"requestType": "UpdatePhraseSetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).patch": "/v2/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "(google.api.http).body": "phrase_set", "(google.api.method_signature)": "phrase_set,update_mask", "(google.longrunning.operation_info).response_type": "PhraseSet", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"patch": "/v2/{phrase_set.name=projects/*/locations/*/phraseSets/*}", "body": "phrase_set"}}, {"(google.api.method_signature)": "phrase_set,update_mask"}, {"(google.longrunning.operation_info)": {"response_type": "PhraseSet", "metadata_type": "OperationMetadata"}}]}, "DeletePhraseSet": {"requestType": "DeletePhraseSetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).delete": "/v2/{name=projects/*/locations/*/phraseSets/*}", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "PhraseSet", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v2/{name=projects/*/locations/*/phraseSets/*}"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "PhraseSet", "metadata_type": "OperationMetadata"}}]}, "UndeletePhraseSet": {"requestType": "UndeletePhraseSetRequest", "responseType": "google.longrunning.Operation", "options": {"(google.api.http).post": "/v2/{name=projects/*/locations/*/phraseSets/*}:undelete", "(google.api.http).body": "*", "(google.api.method_signature)": "name", "(google.longrunning.operation_info).response_type": "PhraseSet", "(google.longrunning.operation_info).metadata_type": "OperationMetadata"}, "parsedOptions": [{"(google.api.http)": {"post": "/v2/{name=projects/*/locations/*/phraseSets/*}:undelete", "body": "*"}}, {"(google.api.method_signature)": "name"}, {"(google.longrunning.operation_info)": {"response_type": "PhraseSet", "metadata_type": "OperationMetadata"}}]}}}, "CreateRecognizerRequest": {"fields": {"recognizer": {"type": "Recognizer", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "validateOnly": {"type": "bool", "id": 2}, "recognizerId": {"type": "string", "id": 3}, "parent": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/Recognizer"}}}}, "OperationMetadata": {"oneofs": {"request": {"oneof": ["batchRecognizeRequest", "createRecognizerRequest", "updateRecognizerRequest", "deleteRecognizerRequest", "undeleteRecognizerRequest", "createCustomClassRequest", "updateCustomClassRequest", "deleteCustomClassRequest", "undeleteCustomClassRequest", "createPhraseSetRequest", "updatePhraseSetRequest", "deletePhraseSetRequest", "undeletePhraseSetRequest", "updateConfigRequest"]}, "metadata": {"oneof": ["batchRecognizeMetadata"]}}, "fields": {"createTime": {"type": "google.protobuf.Timestamp", "id": 1}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 2}, "resource": {"type": "string", "id": 3}, "method": {"type": "string", "id": 4}, "kmsKeyName": {"type": "string", "id": 6, "options": {"(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 7, "options": {"(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}, "batchRecognizeRequest": {"type": "BatchRecognizeRequest", "id": 8}, "createRecognizerRequest": {"type": "CreateRecognizerRequest", "id": 9}, "updateRecognizerRequest": {"type": "UpdateRecognizerRequest", "id": 10}, "deleteRecognizerRequest": {"type": "DeleteRecognizerRequest", "id": 11}, "undeleteRecognizerRequest": {"type": "UndeleteRecognizerRequest", "id": 12}, "createCustomClassRequest": {"type": "CreateCustomClassRequest", "id": 13}, "updateCustomClassRequest": {"type": "UpdateCustomClassRequest", "id": 14}, "deleteCustomClassRequest": {"type": "DeleteCustomClassRequest", "id": 15}, "undeleteCustomClassRequest": {"type": "UndeleteCustomClassRequest", "id": 16}, "createPhraseSetRequest": {"type": "CreatePhraseSetRequest", "id": 17}, "updatePhraseSetRequest": {"type": "UpdatePhraseSetRequest", "id": 18}, "deletePhraseSetRequest": {"type": "DeletePhraseSetRequest", "id": 19}, "undeletePhraseSetRequest": {"type": "UndeletePhraseSetRequest", "id": 20}, "updateConfigRequest": {"type": "UpdateConfigRequest", "id": 21, "options": {"deprecated": true}}, "progressPercent": {"type": "int32", "id": 22}, "batchRecognizeMetadata": {"type": "BatchRecognizeMetadata", "id": 23}}}, "ListRecognizersRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}, "showDeleted": {"type": "bool", "id": 4}}}, "ListRecognizersResponse": {"fields": {"recognizers": {"rule": "repeated", "type": "Recognizer", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetRecognizerRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}}}, "UpdateRecognizerRequest": {"fields": {"recognizer": {"type": "Recognizer", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}, "validateOnly": {"type": "bool", "id": 4}}}, "DeleteRecognizerRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}, "validateOnly": {"type": "bool", "id": 2}, "allowMissing": {"type": "bool", "id": 4}, "etag": {"type": "string", "id": 3}}}, "UndeleteRecognizerRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}, "validateOnly": {"type": "bool", "id": 3}, "etag": {"type": "string", "id": 4}}}, "Recognizer": {"options": {"(google.api.resource).type": "speech.googleapis.com/Recognizer", "(google.api.resource).pattern": "projects/{project}/locations/{location}/recognizers/{recognizer}", "(google.api.resource).style": "DECLARATIVE_FRIENDLY"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "IDENTIFIER"}}, "uid": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "displayName": {"type": "string", "id": 3}, "model": {"type": "string", "id": 4, "options": {"deprecated": true, "(google.api.field_behavior)": "OPTIONAL"}}, "languageCodes": {"rule": "repeated", "type": "string", "id": 17, "options": {"deprecated": true, "(google.api.field_behavior)": "OPTIONAL"}}, "defaultRecognitionConfig": {"type": "RecognitionConfig", "id": 6}, "annotations": {"keyType": "string", "type": "string", "id": 7}, "state": {"type": "State", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 10, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 11, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 14, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "etag": {"type": "string", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "reconciling": {"type": "bool", "id": 13, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "kmsKeyName": {"type": "string", "id": 15, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 16, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}}, "nested": {"State": {"values": {"STATE_UNSPECIFIED": 0, "ACTIVE": 2, "DELETED": 4}}}}, "AutoDetectDecodingConfig": {"fields": {}}, "ExplicitDecodingConfig": {"fields": {"encoding": {"type": "AudioEncoding", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "sampleRateHertz": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "audioChannelCount": {"type": "int32", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}, "nested": {"AudioEncoding": {"values": {"AUDIO_ENCODING_UNSPECIFIED": 0, "LINEAR16": 1, "MULAW": 2, "ALAW": 3, "AMR": 4, "AMR_WB": 5, "FLAC": 6, "MP3": 7, "OGG_OPUS": 8, "WEBM_OPUS": 9, "MP4_AAC": 10, "M4A_AAC": 11, "MOV_AAC": 12}}}}, "SpeakerDiarizationConfig": {"fields": {"minSpeakerCount": {"type": "int32", "id": 2, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "maxSpeakerCount": {"type": "int32", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "RecognitionFeatures": {"fields": {"profanityFilter": {"type": "bool", "id": 1}, "enableWordTimeOffsets": {"type": "bool", "id": 2}, "enableWordConfidence": {"type": "bool", "id": 3}, "enableAutomaticPunctuation": {"type": "bool", "id": 4}, "enableSpokenPunctuation": {"type": "bool", "id": 14}, "enableSpokenEmojis": {"type": "bool", "id": 15}, "multiChannelMode": {"type": "MultiChannelMode", "id": 17}, "diarizationConfig": {"type": "SpeakerDiarizationConfig", "id": 9}, "maxAlternatives": {"type": "int32", "id": 16}}, "nested": {"MultiChannelMode": {"values": {"MULTI_CHANNEL_MODE_UNSPECIFIED": 0, "SEPARATE_RECOGNITION_PER_CHANNEL": 1}}}}, "TranscriptNormalization": {"fields": {"entries": {"rule": "repeated", "type": "Entry", "id": 1}}, "nested": {"Entry": {"fields": {"search": {"type": "string", "id": 1}, "replace": {"type": "string", "id": 2}, "caseSensitive": {"type": "bool", "id": 3}}}}}, "TranslationConfig": {"fields": {"targetLanguage": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}}}, "SpeechAdaptation": {"fields": {"phraseSets": {"rule": "repeated", "type": "AdaptationPhraseSet", "id": 1}, "customClasses": {"rule": "repeated", "type": "CustomClass", "id": 2}}, "nested": {"AdaptationPhraseSet": {"oneofs": {"value": {"oneof": ["phraseSet", "inlinePhraseSet"]}}, "fields": {"phraseSet": {"type": "string", "id": 1, "options": {"(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}, "inlinePhraseSet": {"type": "PhraseSet", "id": 2}}}}}, "RecognitionConfig": {"oneofs": {"decodingConfig": {"oneof": ["autoDecodingConfig", "explicitDecodingConfig"]}}, "fields": {"autoDecodingConfig": {"type": "AutoDetectDecodingConfig", "id": 7}, "explicitDecodingConfig": {"type": "ExplicitDecodingConfig", "id": 8}, "model": {"type": "string", "id": 9, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "languageCodes": {"rule": "repeated", "type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "features": {"type": "RecognitionFeatures", "id": 2}, "adaptation": {"type": "SpeechAdaptation", "id": 6}, "transcriptNormalization": {"type": "TranscriptNormalization", "id": 11, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "translationConfig": {"type": "TranslationConfig", "id": 15, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "RecognizeRequest": {"oneofs": {"audioSource": {"oneof": ["content", "uri"]}}, "fields": {"recognizer": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}, "config": {"type": "RecognitionConfig", "id": 1}, "configMask": {"type": "google.protobuf.FieldMask", "id": 8}, "content": {"type": "bytes", "id": 5}, "uri": {"type": "string", "id": 6}}}, "RecognitionResponseMetadata": {"fields": {"requestId": {"type": "string", "id": 9, "options": {"(google.api.field_info).format": "UUID4"}}, "totalBilledDuration": {"type": "google.protobuf.Duration", "id": 6}}}, "SpeechRecognitionAlternative": {"fields": {"transcript": {"type": "string", "id": 1}, "confidence": {"type": "float", "id": 2}, "words": {"rule": "repeated", "type": "WordInfo", "id": 3}}}, "WordInfo": {"fields": {"startOffset": {"type": "google.protobuf.Duration", "id": 1}, "endOffset": {"type": "google.protobuf.Duration", "id": 2}, "word": {"type": "string", "id": 3}, "confidence": {"type": "float", "id": 4}, "speakerLabel": {"type": "string", "id": 6}}}, "SpeechRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "channelTag": {"type": "int32", "id": 2}, "resultEndOffset": {"type": "google.protobuf.Duration", "id": 4}, "languageCode": {"type": "string", "id": 5, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "RecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 3}, "metadata": {"type": "RecognitionResponseMetadata", "id": 2}}}, "StreamingRecognitionFeatures": {"fields": {"enableVoiceActivityEvents": {"type": "bool", "id": 1}, "interimResults": {"type": "bool", "id": 2}, "voiceActivityTimeout": {"type": "VoiceActivityTimeout", "id": 3}}, "nested": {"VoiceActivityTimeout": {"fields": {"speechStartTimeout": {"type": "google.protobuf.Duration", "id": 1}, "speechEndTimeout": {"type": "google.protobuf.Duration", "id": 2}}}}}, "StreamingRecognitionConfig": {"fields": {"config": {"type": "RecognitionConfig", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "configMask": {"type": "google.protobuf.FieldMask", "id": 3}, "streamingFeatures": {"type": "StreamingRecognitionFeatures", "id": 2}}}, "StreamingRecognizeRequest": {"oneofs": {"streamingRequest": {"oneof": ["streamingConfig", "audio"]}}, "fields": {"recognizer": {"type": "string", "id": 3, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}, "streamingConfig": {"type": "StreamingRecognitionConfig", "id": 6}, "audio": {"type": "bytes", "id": 5}}}, "BatchRecognizeRequest": {"fields": {"recognizer": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Recognizer"}}, "config": {"type": "RecognitionConfig", "id": 4}, "configMask": {"type": "google.protobuf.FieldMask", "id": 5}, "files": {"rule": "repeated", "type": "BatchRecognizeFileMetadata", "id": 3}, "recognitionOutputConfig": {"type": "RecognitionOutputConfig", "id": 6}, "processingStrategy": {"type": "ProcessingStrategy", "id": 7}}, "nested": {"ProcessingStrategy": {"values": {"PROCESSING_STRATEGY_UNSPECIFIED": 0, "DYNAMIC_BATCHING": 1}}}}, "GcsOutputConfig": {"fields": {"uri": {"type": "string", "id": 1}}}, "InlineOutputConfig": {"fields": {}}, "NativeOutputFileFormatConfig": {"fields": {}}, "VttOutputFileFormatConfig": {"fields": {}}, "SrtOutputFileFormatConfig": {"fields": {}}, "OutputFormatConfig": {"fields": {"native": {"type": "NativeOutputFileFormatConfig", "id": 1}, "vtt": {"type": "VttOutputFileFormatConfig", "id": 2}, "srt": {"type": "SrtOutputFileFormatConfig", "id": 3}}}, "RecognitionOutputConfig": {"oneofs": {"output": {"oneof": ["gcsOutputConfig", "inlineResponseConfig"]}}, "fields": {"gcsOutputConfig": {"type": "GcsOutputConfig", "id": 1}, "inlineResponseConfig": {"type": "InlineOutputConfig", "id": 2}, "outputFormatConfig": {"type": "OutputFormatConfig", "id": 3, "options": {"(google.api.field_behavior)": "OPTIONAL"}}}}, "BatchRecognizeResponse": {"fields": {"results": {"keyType": "string", "type": "BatchRecognizeFileResult", "id": 1}, "totalBilledDuration": {"type": "google.protobuf.Duration", "id": 2}}}, "BatchRecognizeResults": {"fields": {"results": {"rule": "repeated", "type": "SpeechRecognitionResult", "id": 1}, "metadata": {"type": "RecognitionResponseMetadata", "id": 2}}}, "CloudStorageResult": {"fields": {"uri": {"type": "string", "id": 1}, "vttFormatUri": {"type": "string", "id": 2}, "srtFormatUri": {"type": "string", "id": 3}}}, "InlineResult": {"fields": {"transcript": {"type": "BatchRecognizeResults", "id": 1}, "vttCaptions": {"type": "string", "id": 2}, "srtCaptions": {"type": "string", "id": 3}}}, "BatchRecognizeFileResult": {"oneofs": {"result": {"oneof": ["cloudStorageResult", "inlineResult"]}}, "fields": {"error": {"type": "google.rpc.Status", "id": 2}, "metadata": {"type": "RecognitionResponseMetadata", "id": 3}, "cloudStorageResult": {"type": "CloudStorageResult", "id": 5}, "inlineResult": {"type": "InlineResult", "id": 6}, "uri": {"type": "string", "id": 1, "options": {"deprecated": true}}, "transcript": {"type": "BatchRecognizeResults", "id": 4, "options": {"deprecated": true}}}}, "BatchRecognizeTranscriptionMetadata": {"fields": {"progressPercent": {"type": "int32", "id": 1}, "error": {"type": "google.rpc.Status", "id": 2}, "uri": {"type": "string", "id": 3}}}, "BatchRecognizeMetadata": {"fields": {"transcriptionMetadata": {"keyType": "string", "type": "BatchRecognizeTranscriptionMetadata", "id": 1}}}, "BatchRecognizeFileMetadata": {"oneofs": {"audioSource": {"oneof": ["uri"]}}, "fields": {"uri": {"type": "string", "id": 1}, "config": {"type": "RecognitionConfig", "id": 4}, "configMask": {"type": "google.protobuf.FieldMask", "id": 5}}}, "StreamingRecognitionResult": {"fields": {"alternatives": {"rule": "repeated", "type": "SpeechRecognitionAlternative", "id": 1}, "isFinal": {"type": "bool", "id": 2}, "stability": {"type": "float", "id": 3}, "resultEndOffset": {"type": "google.protobuf.Duration", "id": 4}, "channelTag": {"type": "int32", "id": 5}, "languageCode": {"type": "string", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "StreamingRecognizeResponse": {"fields": {"results": {"rule": "repeated", "type": "StreamingRecognitionResult", "id": 6}, "speechEventType": {"type": "SpeechEventType", "id": 3}, "speechEventOffset": {"type": "google.protobuf.Duration", "id": 7}, "metadata": {"type": "RecognitionResponseMetadata", "id": 5}}, "nested": {"SpeechEventType": {"values": {"SPEECH_EVENT_TYPE_UNSPECIFIED": 0, "END_OF_SINGLE_UTTERANCE": 1, "SPEECH_ACTIVITY_BEGIN": 2, "SPEECH_ACTIVITY_END": 3}}}}, "Config": {"options": {"(google.api.resource).type": "speech.googleapis.com/Config", "(google.api.resource).pattern": "projects/{project}/locations/{location}/config"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "IDENTIFIER"}}, "kmsKeyName": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OPTIONAL", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 3, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}}}, "GetConfigRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/Config"}}}}, "UpdateConfigRequest": {"fields": {"config": {"type": "Config", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}}}, "CustomClass": {"options": {"(google.api.resource).type": "speech.googleapis.com/CustomClass", "(google.api.resource).pattern": "projects/{project}/locations/{location}/customClasses/{custom_class}", "(google.api.resource).style": "DECLARATIVE_FRIENDLY"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "IDENTIFIER"}}, "uid": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "displayName": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "items": {"rule": "repeated", "type": "ClassItem", "id": 5}, "state": {"type": "State", "id": 15, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "annotations": {"keyType": "string", "type": "string", "id": 10, "options": {"(google.api.field_behavior)": "OPTIONAL"}}, "etag": {"type": "string", "id": 11, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "reconciling": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "kmsKeyName": {"type": "string", "id": 13, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 14, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}}, "nested": {"ClassItem": {"fields": {"value": {"type": "string", "id": 1}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "ACTIVE": 2, "DELETED": 4}}}}, "PhraseSet": {"options": {"(google.api.resource).type": "speech.googleapis.com/PhraseSet", "(google.api.resource).pattern": "projects/{project}/locations/{location}/phraseSets/{phrase_set}", "(google.api.resource).style": "DECLARATIVE_FRIENDLY"}, "fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "IDENTIFIER"}}, "uid": {"type": "string", "id": 2, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "phrases": {"rule": "repeated", "type": "Phrase", "id": 3}, "boost": {"type": "float", "id": 4}, "displayName": {"type": "string", "id": 5}, "state": {"type": "State", "id": 15, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "createTime": {"type": "google.protobuf.Timestamp", "id": 6, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "updateTime": {"type": "google.protobuf.Timestamp", "id": 7, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "deleteTime": {"type": "google.protobuf.Timestamp", "id": 8, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "expireTime": {"type": "google.protobuf.Timestamp", "id": 9, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "annotations": {"keyType": "string", "type": "string", "id": 10}, "etag": {"type": "string", "id": 11, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "reconciling": {"type": "bool", "id": 12, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY"}}, "kmsKeyName": {"type": "string", "id": 13, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKey"}}, "kmsKeyVersionName": {"type": "string", "id": 14, "options": {"(google.api.field_behavior)": "OUTPUT_ONLY", "(google.api.resource_reference).type": "cloudkms.googleapis.com/CryptoKeyVersion"}}}, "nested": {"Phrase": {"fields": {"value": {"type": "string", "id": 1}, "boost": {"type": "float", "id": 2}}}, "State": {"values": {"STATE_UNSPECIFIED": 0, "ACTIVE": 2, "DELETED": 4}}}}, "CreateCustomClassRequest": {"fields": {"customClass": {"type": "CustomClass", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "validateOnly": {"type": "bool", "id": 2}, "customClassId": {"type": "string", "id": 3}, "parent": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/CustomClass"}}}}, "ListCustomClassesRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}, "showDeleted": {"type": "bool", "id": 4}}}, "ListCustomClassesResponse": {"fields": {"customClasses": {"rule": "repeated", "type": "CustomClass", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}}}, "UpdateCustomClassRequest": {"fields": {"customClass": {"type": "CustomClass", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}, "validateOnly": {"type": "bool", "id": 4}}}, "DeleteCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}, "validateOnly": {"type": "bool", "id": 2}, "allowMissing": {"type": "bool", "id": 4}, "etag": {"type": "string", "id": 3}}}, "UndeleteCustomClassRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/CustomClass"}}, "validateOnly": {"type": "bool", "id": 3}, "etag": {"type": "string", "id": 4}}}, "CreatePhraseSetRequest": {"fields": {"phraseSet": {"type": "PhraseSet", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "validateOnly": {"type": "bool", "id": 2}, "phraseSetId": {"type": "string", "id": 3}, "parent": {"type": "string", "id": 4, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).child_type": "speech.googleapis.com/PhraseSet"}}}}, "ListPhraseSetsRequest": {"fields": {"parent": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "locations.googleapis.com/Location"}}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}, "showDeleted": {"type": "bool", "id": 4}}}, "ListPhraseSetsResponse": {"fields": {"phraseSets": {"rule": "repeated", "type": "PhraseSet", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "GetPhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}}}, "UpdatePhraseSetRequest": {"fields": {"phraseSet": {"type": "PhraseSet", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED"}}, "updateMask": {"type": "google.protobuf.FieldMask", "id": 2}, "validateOnly": {"type": "bool", "id": 4}}}, "DeletePhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}, "validateOnly": {"type": "bool", "id": 2}, "allowMissing": {"type": "bool", "id": 4}, "etag": {"type": "string", "id": 3}}}, "UndeletePhraseSetRequest": {"fields": {"name": {"type": "string", "id": 1, "options": {"(google.api.field_behavior)": "REQUIRED", "(google.api.resource_reference).type": "speech.googleapis.com/PhraseSet"}}, "validateOnly": {"type": "bool", "id": 3}, "etag": {"type": "string", "id": 4}}}, "ModelFeature": {"fields": {"feature": {"type": "string", "id": 1}, "releaseState": {"type": "string", "id": 2}}}, "ModelFeatures": {"fields": {"modelFeature": {"rule": "repeated", "type": "ModelFeature", "id": 1}}}, "ModelMetadata": {"fields": {"modelFeatures": {"keyType": "string", "type": "ModelFeatures", "id": 1}}}, "LanguageMetadata": {"fields": {"models": {"keyType": "string", "type": "ModelMetadata", "id": 1}}}, "AccessMetadata": {"fields": {"constraintType": {"type": "ConstraintType", "id": 1}}, "nested": {"ConstraintType": {"values": {"CONSTRAINT_TYPE_UNSPECIFIED": 0, "RESOURCE_LOCATIONS_ORG_POLICY_CREATE_CONSTRAINT": 1}}}}, "LocationsMetadata": {"fields": {"languages": {"type": "LanguageMetadata", "id": 1}, "accessMetadata": {"type": "AccessMetadata", "id": 2}}}}}}}}}, "api": {"options": {"go_package": "google.golang.org/genproto/googleapis/api/annotations;annotations", "java_multiple_files": true, "java_outer_classname": "FieldInfoProto", "java_package": "com.google.api", "objc_class_prefix": "GAPI", "cc_enable_arenas": true}, "nested": {"http": {"type": "HttpRule", "id": 72295728, "extend": "google.protobuf.MethodOptions"}, "Http": {"fields": {"rules": {"rule": "repeated", "type": "HttpRule", "id": 1}, "fullyDecodeReservedExpansion": {"type": "bool", "id": 2}}}, "HttpRule": {"oneofs": {"pattern": {"oneof": ["get", "put", "post", "delete", "patch", "custom"]}}, "fields": {"selector": {"type": "string", "id": 1}, "get": {"type": "string", "id": 2}, "put": {"type": "string", "id": 3}, "post": {"type": "string", "id": 4}, "delete": {"type": "string", "id": 5}, "patch": {"type": "string", "id": 6}, "custom": {"type": "CustomHttpPattern", "id": 8}, "body": {"type": "string", "id": 7}, "responseBody": {"type": "string", "id": 12}, "additionalBindings": {"rule": "repeated", "type": "HttpRule", "id": 11}}}, "CustomHttpPattern": {"fields": {"kind": {"type": "string", "id": 1}, "path": {"type": "string", "id": 2}}}, "methodSignature": {"rule": "repeated", "type": "string", "id": 1051, "extend": "google.protobuf.MethodOptions"}, "defaultHost": {"type": "string", "id": 1049, "extend": "google.protobuf.ServiceOptions"}, "oauthScopes": {"type": "string", "id": 1050, "extend": "google.protobuf.ServiceOptions"}, "apiVersion": {"type": "string", "id": 525000001, "extend": "google.protobuf.ServiceOptions"}, "CommonLanguageSettings": {"fields": {"referenceDocsUri": {"type": "string", "id": 1, "options": {"deprecated": true}}, "destinations": {"rule": "repeated", "type": "ClientLibraryDestination", "id": 2}}}, "ClientLibrarySettings": {"fields": {"version": {"type": "string", "id": 1}, "launchStage": {"type": "LaunchStage", "id": 2}, "restNumericEnums": {"type": "bool", "id": 3}, "javaSettings": {"type": "JavaSettings", "id": 21}, "cppSettings": {"type": "CppSettings", "id": 22}, "phpSettings": {"type": "PhpSettings", "id": 23}, "pythonSettings": {"type": "PythonSettings", "id": 24}, "nodeSettings": {"type": "NodeSettings", "id": 25}, "dotnetSettings": {"type": "DotnetSettings", "id": 26}, "rubySettings": {"type": "RubySettings", "id": 27}, "goSettings": {"type": "GoSettings", "id": 28}}}, "Publishing": {"fields": {"methodSettings": {"rule": "repeated", "type": "MethodSettings", "id": 2}, "newIssueUri": {"type": "string", "id": 101}, "documentationUri": {"type": "string", "id": 102}, "apiShortName": {"type": "string", "id": 103}, "githubLabel": {"type": "string", "id": 104}, "codeownerGithubTeams": {"rule": "repeated", "type": "string", "id": 105}, "docTagPrefix": {"type": "string", "id": 106}, "organization": {"type": "ClientLibraryOrganization", "id": 107}, "librarySettings": {"rule": "repeated", "type": "ClientLibrarySettings", "id": 109}, "protoReferenceDocumentationUri": {"type": "string", "id": 110}, "restReferenceDocumentationUri": {"type": "string", "id": 111}}}, "JavaSettings": {"fields": {"libraryPackage": {"type": "string", "id": 1}, "serviceClassNames": {"keyType": "string", "type": "string", "id": 2}, "common": {"type": "CommonLanguageSettings", "id": 3}}}, "CppSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PhpSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "PythonSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "NodeSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "DotnetSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}, "renamedServices": {"keyType": "string", "type": "string", "id": 2}, "renamedResources": {"keyType": "string", "type": "string", "id": 3}, "ignoredResources": {"rule": "repeated", "type": "string", "id": 4}, "forcedNamespaceAliases": {"rule": "repeated", "type": "string", "id": 5}, "handwrittenSignatures": {"rule": "repeated", "type": "string", "id": 6}}}, "RubySettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "GoSettings": {"fields": {"common": {"type": "CommonLanguageSettings", "id": 1}}}, "MethodSettings": {"fields": {"selector": {"type": "string", "id": 1}, "longRunning": {"type": "<PERSON><PERSON><PERSON>ning", "id": 2}, "autoPopulatedFields": {"rule": "repeated", "type": "string", "id": 3}}, "nested": {"LongRunning": {"fields": {"initialPollDelay": {"type": "google.protobuf.Duration", "id": 1}, "pollDelayMultiplier": {"type": "float", "id": 2}, "maxPollDelay": {"type": "google.protobuf.Duration", "id": 3}, "totalPollTimeout": {"type": "google.protobuf.Duration", "id": 4}}}}}, "ClientLibraryOrganization": {"values": {"CLIENT_LIBRARY_ORGANIZATION_UNSPECIFIED": 0, "CLOUD": 1, "ADS": 2, "PHOTOS": 3, "STREET_VIEW": 4, "SHOPPING": 5, "GEO": 6, "GENERATIVE_AI": 7}}, "ClientLibraryDestination": {"values": {"CLIENT_LIBRARY_DESTINATION_UNSPECIFIED": 0, "GITHUB": 10, "PACKAGE_MANAGER": 20}}, "LaunchStage": {"values": {"LAUNCH_STAGE_UNSPECIFIED": 0, "UNIMPLEMENTED": 6, "PRELAUNCH": 7, "EARLY_ACCESS": 1, "ALPHA": 2, "BETA": 3, "GA": 4, "DEPRECATED": 5}}, "fieldBehavior": {"rule": "repeated", "type": "google.api.FieldBehavior", "id": 1052, "extend": "google.protobuf.FieldOptions", "options": {"packed": false}}, "FieldBehavior": {"values": {"FIELD_BEHAVIOR_UNSPECIFIED": 0, "OPTIONAL": 1, "REQUIRED": 2, "OUTPUT_ONLY": 3, "INPUT_ONLY": 4, "IMMUTABLE": 5, "UNORDERED_LIST": 6, "NON_EMPTY_DEFAULT": 7, "IDENTIFIER": 8}}, "resourceReference": {"type": "google.api.ResourceReference", "id": 1055, "extend": "google.protobuf.FieldOptions"}, "resourceDefinition": {"rule": "repeated", "type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.FileOptions"}, "resource": {"type": "google.api.ResourceDescriptor", "id": 1053, "extend": "google.protobuf.MessageOptions"}, "ResourceDescriptor": {"fields": {"type": {"type": "string", "id": 1}, "pattern": {"rule": "repeated", "type": "string", "id": 2}, "nameField": {"type": "string", "id": 3}, "history": {"type": "History", "id": 4}, "plural": {"type": "string", "id": 5}, "singular": {"type": "string", "id": 6}, "style": {"rule": "repeated", "type": "Style", "id": 10}}, "nested": {"History": {"values": {"HISTORY_UNSPECIFIED": 0, "ORIGINALLY_SINGLE_PATTERN": 1, "FUTURE_MULTI_PATTERN": 2}}, "Style": {"values": {"STYLE_UNSPECIFIED": 0, "DECLARATIVE_FRIENDLY": 1}}}}, "ResourceReference": {"fields": {"type": {"type": "string", "id": 1}, "childType": {"type": "string", "id": 2}}}, "fieldInfo": {"type": "google.api.FieldInfo", "id": 291403980, "extend": "google.protobuf.FieldOptions"}, "FieldInfo": {"fields": {"format": {"type": "Format", "id": 1}}, "nested": {"Format": {"values": {"FORMAT_UNSPECIFIED": 0, "UUID4": 1, "IPV4": 2, "IPV6": 3, "IPV4_OR_IPV6": 4}}}}}}, "protobuf": {"options": {"go_package": "google.golang.org/protobuf/types/descriptorpb", "java_package": "com.google.protobuf", "java_outer_classname": "DescriptorProtos", "csharp_namespace": "Google.Protobuf.Reflection", "objc_class_prefix": "GPB", "cc_enable_arenas": true, "optimize_for": "SPEED"}, "nested": {"FileDescriptorSet": {"edition": "proto2", "fields": {"file": {"rule": "repeated", "type": "FileDescriptorProto", "id": 1}}}, "Edition": {"edition": "proto2", "values": {"EDITION_UNKNOWN": 0, "EDITION_PROTO2": 998, "EDITION_PROTO3": 999, "EDITION_2023": 1000, "EDITION_2024": 1001, "EDITION_1_TEST_ONLY": 1, "EDITION_2_TEST_ONLY": 2, "EDITION_99997_TEST_ONLY": 99997, "EDITION_99998_TEST_ONLY": 99998, "EDITION_99999_TEST_ONLY": 99999, "EDITION_MAX": 2147483647}}, "FileDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "package": {"type": "string", "id": 2}, "dependency": {"rule": "repeated", "type": "string", "id": 3}, "publicDependency": {"rule": "repeated", "type": "int32", "id": 10}, "weakDependency": {"rule": "repeated", "type": "int32", "id": 11}, "messageType": {"rule": "repeated", "type": "DescriptorProto", "id": 4}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 5}, "service": {"rule": "repeated", "type": "ServiceDescriptorProto", "id": 6}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 7}, "options": {"type": "FileOptions", "id": 8}, "sourceCodeInfo": {"type": "SourceCodeInfo", "id": 9}, "syntax": {"type": "string", "id": 12}, "edition": {"type": "Edition", "id": 14}}}, "DescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "field": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 2}, "extension": {"rule": "repeated", "type": "FieldDescriptorProto", "id": 6}, "nestedType": {"rule": "repeated", "type": "DescriptorProto", "id": 3}, "enumType": {"rule": "repeated", "type": "EnumDescriptorProto", "id": 4}, "extensionRange": {"rule": "repeated", "type": "ExtensionRange", "id": 5}, "oneofDecl": {"rule": "repeated", "type": "OneofDescriptorProto", "id": 8}, "options": {"type": "MessageOptions", "id": 7}, "reservedRange": {"rule": "repeated", "type": "ReservedRange", "id": 9}, "reservedName": {"rule": "repeated", "type": "string", "id": 10}}, "nested": {"ExtensionRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}, "options": {"type": "ExtensionRangeOptions", "id": 3}}}, "ReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "ExtensionRangeOptions": {"edition": "proto2", "fields": {"uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}, "declaration": {"rule": "repeated", "type": "Declaration", "id": 2, "options": {"retention": "RETENTION_SOURCE"}}, "features": {"type": "FeatureSet", "id": 50}, "verification": {"type": "VerificationState", "id": 3, "options": {"default": "UNVERIFIED", "retention": "RETENTION_SOURCE"}}}, "extensions": [[1000, 536870911]], "nested": {"Declaration": {"fields": {"number": {"type": "int32", "id": 1}, "fullName": {"type": "string", "id": 2}, "type": {"type": "string", "id": 3}, "reserved": {"type": "bool", "id": 5}, "repeated": {"type": "bool", "id": 6}}, "reserved": [[4, 4]]}, "VerificationState": {"values": {"DECLARATION": 0, "UNVERIFIED": 1}}}}, "FieldDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 3}, "label": {"type": "Label", "id": 4}, "type": {"type": "Type", "id": 5}, "typeName": {"type": "string", "id": 6}, "extendee": {"type": "string", "id": 2}, "defaultValue": {"type": "string", "id": 7}, "oneofIndex": {"type": "int32", "id": 9}, "jsonName": {"type": "string", "id": 10}, "options": {"type": "FieldOptions", "id": 8}, "proto3Optional": {"type": "bool", "id": 17}}, "nested": {"Type": {"values": {"TYPE_DOUBLE": 1, "TYPE_FLOAT": 2, "TYPE_INT64": 3, "TYPE_UINT64": 4, "TYPE_INT32": 5, "TYPE_FIXED64": 6, "TYPE_FIXED32": 7, "TYPE_BOOL": 8, "TYPE_STRING": 9, "TYPE_GROUP": 10, "TYPE_MESSAGE": 11, "TYPE_BYTES": 12, "TYPE_UINT32": 13, "TYPE_ENUM": 14, "TYPE_SFIXED32": 15, "TYPE_SFIXED64": 16, "TYPE_SINT32": 17, "TYPE_SINT64": 18}}, "Label": {"values": {"LABEL_OPTIONAL": 1, "LABEL_REPEATED": 3, "LABEL_REQUIRED": 2}}}}, "OneofDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "options": {"type": "OneofOptions", "id": 2}}}, "EnumDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "value": {"rule": "repeated", "type": "EnumValueDescriptorProto", "id": 2}, "options": {"type": "EnumOptions", "id": 3}, "reservedRange": {"rule": "repeated", "type": "EnumReservedRange", "id": 4}, "reservedName": {"rule": "repeated", "type": "string", "id": 5}}, "nested": {"EnumReservedRange": {"fields": {"start": {"type": "int32", "id": 1}, "end": {"type": "int32", "id": 2}}}}}, "EnumValueDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "number": {"type": "int32", "id": 2}, "options": {"type": "EnumValueOptions", "id": 3}}}, "ServiceDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "method": {"rule": "repeated", "type": "MethodDescriptorProto", "id": 2}, "options": {"type": "ServiceOptions", "id": 3}}}, "MethodDescriptorProto": {"edition": "proto2", "fields": {"name": {"type": "string", "id": 1}, "inputType": {"type": "string", "id": 2}, "outputType": {"type": "string", "id": 3}, "options": {"type": "MethodOptions", "id": 4}, "clientStreaming": {"type": "bool", "id": 5, "options": {"default": false}}, "serverStreaming": {"type": "bool", "id": 6, "options": {"default": false}}}}, "FileOptions": {"edition": "proto2", "fields": {"javaPackage": {"type": "string", "id": 1}, "javaOuterClassname": {"type": "string", "id": 8}, "javaMultipleFiles": {"type": "bool", "id": 10, "options": {"default": false}}, "javaGenerateEqualsAndHash": {"type": "bool", "id": 20, "options": {"deprecated": true}}, "javaStringCheckUtf8": {"type": "bool", "id": 27, "options": {"default": false}}, "optimizeFor": {"type": "OptimizeMode", "id": 9, "options": {"default": "SPEED"}}, "goPackage": {"type": "string", "id": 11}, "ccGenericServices": {"type": "bool", "id": 16, "options": {"default": false}}, "javaGenericServices": {"type": "bool", "id": 17, "options": {"default": false}}, "pyGenericServices": {"type": "bool", "id": 18, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 23, "options": {"default": false}}, "ccEnableArenas": {"type": "bool", "id": 31, "options": {"default": true}}, "objcClassPrefix": {"type": "string", "id": 36}, "csharpNamespace": {"type": "string", "id": 37}, "swiftPrefix": {"type": "string", "id": 39}, "phpClassPrefix": {"type": "string", "id": 40}, "phpNamespace": {"type": "string", "id": 41}, "phpMetadataNamespace": {"type": "string", "id": 44}, "rubyPackage": {"type": "string", "id": 45}, "features": {"type": "FeatureSet", "id": 50}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[42, 42], [38, 38]], "nested": {"OptimizeMode": {"values": {"SPEED": 1, "CODE_SIZE": 2, "LITE_RUNTIME": 3}}}}, "MessageOptions": {"edition": "proto2", "fields": {"messageSetWireFormat": {"type": "bool", "id": 1, "options": {"default": false}}, "noStandardDescriptorAccessor": {"type": "bool", "id": 2, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "mapEntry": {"type": "bool", "id": 7}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 11, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 12}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[4, 4], [5, 5], [6, 6], [8, 8], [9, 9]]}, "FieldOptions": {"edition": "proto2", "fields": {"ctype": {"type": "CType", "id": 1, "options": {"default": "STRING"}}, "packed": {"type": "bool", "id": 2}, "jstype": {"type": "JSType", "id": 6, "options": {"default": "JS_NORMAL"}}, "lazy": {"type": "bool", "id": 5, "options": {"default": false}}, "unverifiedLazy": {"type": "bool", "id": 15, "options": {"default": false}}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "weak": {"type": "bool", "id": 10, "options": {"default": false}}, "debugRedact": {"type": "bool", "id": 16, "options": {"default": false}}, "retention": {"type": "OptionRetention", "id": 17}, "targets": {"rule": "repeated", "type": "OptionTargetType", "id": 19}, "editionDefaults": {"rule": "repeated", "type": "EditionDefault", "id": 20}, "features": {"type": "FeatureSet", "id": 21}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[4, 4], [18, 18]], "nested": {"CType": {"values": {"STRING": 0, "CORD": 1, "STRING_PIECE": 2}}, "JSType": {"values": {"JS_NORMAL": 0, "JS_STRING": 1, "JS_NUMBER": 2}}, "OptionRetention": {"values": {"RETENTION_UNKNOWN": 0, "RETENTION_RUNTIME": 1, "RETENTION_SOURCE": 2}}, "OptionTargetType": {"values": {"TARGET_TYPE_UNKNOWN": 0, "TARGET_TYPE_FILE": 1, "TARGET_TYPE_EXTENSION_RANGE": 2, "TARGET_TYPE_MESSAGE": 3, "TARGET_TYPE_FIELD": 4, "TARGET_TYPE_ONEOF": 5, "TARGET_TYPE_ENUM": 6, "TARGET_TYPE_ENUM_ENTRY": 7, "TARGET_TYPE_SERVICE": 8, "TARGET_TYPE_METHOD": 9}}, "EditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "value": {"type": "string", "id": 2}}}}}, "OneofOptions": {"edition": "proto2", "fields": {"features": {"type": "FeatureSet", "id": 1}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "EnumOptions": {"edition": "proto2", "fields": {"allowAlias": {"type": "bool", "id": 2}, "deprecated": {"type": "bool", "id": 3, "options": {"default": false}}, "deprecatedLegacyJsonFieldConflicts": {"type": "bool", "id": 6, "options": {"deprecated": true}}, "features": {"type": "FeatureSet", "id": 7}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "reserved": [[5, 5]]}, "EnumValueOptions": {"edition": "proto2", "fields": {"deprecated": {"type": "bool", "id": 1, "options": {"default": false}}, "features": {"type": "FeatureSet", "id": 2}, "debugRedact": {"type": "bool", "id": 3, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "ServiceOptions": {"edition": "proto2", "fields": {"features": {"type": "FeatureSet", "id": 34}, "deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]]}, "MethodOptions": {"edition": "proto2", "fields": {"deprecated": {"type": "bool", "id": 33, "options": {"default": false}}, "idempotencyLevel": {"type": "IdempotencyLevel", "id": 34, "options": {"default": "IDEMPOTENCY_UNKNOWN"}}, "features": {"type": "FeatureSet", "id": 35}, "uninterpretedOption": {"rule": "repeated", "type": "UninterpretedOption", "id": 999}}, "extensions": [[1000, 536870911]], "nested": {"IdempotencyLevel": {"values": {"IDEMPOTENCY_UNKNOWN": 0, "NO_SIDE_EFFECTS": 1, "IDEMPOTENT": 2}}}}, "UninterpretedOption": {"edition": "proto2", "fields": {"name": {"rule": "repeated", "type": "NamePart", "id": 2}, "identifierValue": {"type": "string", "id": 3}, "positiveIntValue": {"type": "uint64", "id": 4}, "negativeIntValue": {"type": "int64", "id": 5}, "doubleValue": {"type": "double", "id": 6}, "stringValue": {"type": "bytes", "id": 7}, "aggregateValue": {"type": "string", "id": 8}}, "nested": {"NamePart": {"fields": {"namePart": {"rule": "required", "type": "string", "id": 1}, "isExtension": {"rule": "required", "type": "bool", "id": 2}}}}}, "FeatureSet": {"edition": "proto2", "fields": {"fieldPresence": {"type": "FieldPresence", "id": 1, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_2023", "edition_defaults.value": "EXPLICIT"}}, "enumType": {"type": "EnumType", "id": 2, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "OPEN"}}, "repeatedFieldEncoding": {"type": "RepeatedFieldEncoding", "id": 3, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "PACKED"}}, "utf8Validation": {"type": "Utf8Validation", "id": 4, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "VERIFY"}}, "messageEncoding": {"type": "MessageEncoding", "id": 5, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO2", "edition_defaults.value": "LENGTH_PREFIXED"}}, "jsonFormat": {"type": "JsonFormat", "id": 6, "options": {"retention": "RETENTION_RUNTIME", "targets": "TARGET_TYPE_FILE", "edition_defaults.edition": "EDITION_PROTO3", "edition_defaults.value": "ALLOW"}}}, "extensions": [[1000, 1000], [1001, 1001], [1002, 1002], [9990, 9990], [9995, 9999], [10000, 10000]], "reserved": [[999, 999]], "nested": {"FieldPresence": {"values": {"FIELD_PRESENCE_UNKNOWN": 0, "EXPLICIT": 1, "IMPLICIT": 2, "LEGACY_REQUIRED": 3}}, "EnumType": {"values": {"ENUM_TYPE_UNKNOWN": 0, "OPEN": 1, "CLOSED": 2}}, "RepeatedFieldEncoding": {"values": {"REPEATED_FIELD_ENCODING_UNKNOWN": 0, "PACKED": 1, "EXPANDED": 2}}, "Utf8Validation": {"values": {"UTF8_VALIDATION_UNKNOWN": 0, "VERIFY": 2, "NONE": 3}}, "MessageEncoding": {"values": {"MESSAGE_ENCODING_UNKNOWN": 0, "LENGTH_PREFIXED": 1, "DELIMITED": 2}}, "JsonFormat": {"values": {"JSON_FORMAT_UNKNOWN": 0, "ALLOW": 1, "LEGACY_BEST_EFFORT": 2}}}}, "FeatureSetDefaults": {"edition": "proto2", "fields": {"defaults": {"rule": "repeated", "type": "FeatureSetEditionDefault", "id": 1}, "minimumEdition": {"type": "Edition", "id": 4}, "maximumEdition": {"type": "Edition", "id": 5}}, "nested": {"FeatureSetEditionDefault": {"fields": {"edition": {"type": "Edition", "id": 3}, "features": {"type": "FeatureSet", "id": 2}}}}}, "SourceCodeInfo": {"edition": "proto2", "fields": {"location": {"rule": "repeated", "type": "Location", "id": 1}}, "nested": {"Location": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1, "options": {"packed": true}}, "span": {"rule": "repeated", "type": "int32", "id": 2, "options": {"packed": true}}, "leadingComments": {"type": "string", "id": 3}, "trailingComments": {"type": "string", "id": 4}, "leadingDetachedComments": {"rule": "repeated", "type": "string", "id": 6}}}}}, "GeneratedCodeInfo": {"edition": "proto2", "fields": {"annotation": {"rule": "repeated", "type": "Annotation", "id": 1}}, "nested": {"Annotation": {"fields": {"path": {"rule": "repeated", "type": "int32", "id": 1, "options": {"packed": true}}, "sourceFile": {"type": "string", "id": 2}, "begin": {"type": "int32", "id": 3}, "end": {"type": "int32", "id": 4}, "semantic": {"type": "Semantic", "id": 5}}, "nested": {"Semantic": {"values": {"NONE": 0, "SET": 1, "ALIAS": 2}}}}}}, "Duration": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "Any": {"fields": {"type_url": {"type": "string", "id": 1}, "value": {"type": "bytes", "id": 2}}}, "Empty": {"fields": {}}, "Timestamp": {"fields": {"seconds": {"type": "int64", "id": 1}, "nanos": {"type": "int32", "id": 2}}}, "DoubleValue": {"fields": {"value": {"type": "double", "id": 1}}}, "FloatValue": {"fields": {"value": {"type": "float", "id": 1}}}, "Int64Value": {"fields": {"value": {"type": "int64", "id": 1}}}, "UInt64Value": {"fields": {"value": {"type": "uint64", "id": 1}}}, "Int32Value": {"fields": {"value": {"type": "int32", "id": 1}}}, "UInt32Value": {"fields": {"value": {"type": "uint32", "id": 1}}}, "BoolValue": {"fields": {"value": {"type": "bool", "id": 1}}}, "StringValue": {"fields": {"value": {"type": "string", "id": 1}}}, "BytesValue": {"fields": {"value": {"type": "bytes", "id": 1}}}, "FieldMask": {"fields": {"paths": {"rule": "repeated", "type": "string", "id": 1}}}}}, "longrunning": {"options": {"cc_enable_arenas": true, "csharp_namespace": "Google.LongRunning", "go_package": "cloud.google.com/go/longrunning/autogen/longrunningpb;longrunningpb", "java_multiple_files": true, "java_outer_classname": "OperationsProto", "java_package": "com.google.longrunning", "php_namespace": "Google\\LongRunning"}, "nested": {"operationInfo": {"type": "google.longrunning.OperationInfo", "id": 1049, "extend": "google.protobuf.MethodOptions"}, "Operations": {"options": {"(google.api.default_host)": "longrunning.googleapis.com"}, "methods": {"ListOperations": {"requestType": "ListOperationsRequest", "responseType": "ListOperationsResponse", "options": {"(google.api.http).get": "/v1/{name=operations}", "(google.api.method_signature)": "name,filter"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations}"}}, {"(google.api.method_signature)": "name,filter"}]}, "GetOperation": {"requestType": "GetOperationRequest", "responseType": "Operation", "options": {"(google.api.http).get": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"get": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "DeleteOperation": {"requestType": "DeleteOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).delete": "/v1/{name=operations/**}", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"delete": "/v1/{name=operations/**}"}}, {"(google.api.method_signature)": "name"}]}, "CancelOperation": {"requestType": "CancelOperationRequest", "responseType": "google.protobuf.Empty", "options": {"(google.api.http).post": "/v1/{name=operations/**}:cancel", "(google.api.http).body": "*", "(google.api.method_signature)": "name"}, "parsedOptions": [{"(google.api.http)": {"post": "/v1/{name=operations/**}:cancel", "body": "*"}}, {"(google.api.method_signature)": "name"}]}, "WaitOperation": {"requestType": "WaitOperationRequest", "responseType": "Operation"}}}, "Operation": {"oneofs": {"result": {"oneof": ["error", "response"]}}, "fields": {"name": {"type": "string", "id": 1}, "metadata": {"type": "google.protobuf.Any", "id": 2}, "done": {"type": "bool", "id": 3}, "error": {"type": "google.rpc.Status", "id": 4}, "response": {"type": "google.protobuf.Any", "id": 5}}}, "GetOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "ListOperationsRequest": {"fields": {"name": {"type": "string", "id": 4}, "filter": {"type": "string", "id": 1}, "pageSize": {"type": "int32", "id": 2}, "pageToken": {"type": "string", "id": 3}}}, "ListOperationsResponse": {"fields": {"operations": {"rule": "repeated", "type": "Operation", "id": 1}, "nextPageToken": {"type": "string", "id": 2}}}, "CancelOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "DeleteOperationRequest": {"fields": {"name": {"type": "string", "id": 1}}}, "WaitOperationRequest": {"fields": {"name": {"type": "string", "id": 1}, "timeout": {"type": "google.protobuf.Duration", "id": 2}}}, "OperationInfo": {"fields": {"responseType": {"type": "string", "id": 1}, "metadataType": {"type": "string", "id": 2}}}}}, "rpc": {"options": {"cc_enable_arenas": true, "go_package": "google.golang.org/genproto/googleapis/rpc/status;status", "java_multiple_files": true, "java_outer_classname": "StatusProto", "java_package": "com.google.rpc", "objc_class_prefix": "RPC"}, "nested": {"Status": {"fields": {"code": {"type": "int32", "id": 1}, "message": {"type": "string", "id": 2}, "details": {"rule": "repeated", "type": "google.protobuf.Any", "id": 3}}}}}}}}}