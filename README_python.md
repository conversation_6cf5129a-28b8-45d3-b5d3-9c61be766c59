# Spiritual Chat - Python Version

A voice-enabled spiritual guidance application that uses AI speech recognition and text-to-speech to provide relevant verses from multiple religious traditions.

## Features

- 🎤 **Voice Input**: Uses Google Speech Recognition (with Whisper fallback)
- 🔊 **Voice Output**: Text-to-speech responses using gTTS
- 📚 **Multi-Religion Support**: Christianity, Islam, Judaism, Hinduism, Buddhism
- 🎯 **Smart Topic Detection**: Recognizes 10+ spiritual topics
- 💾 **SQLite Database**: Stores verses efficiently
- 🌍 **Offline Capable**: Whisper model works offline

## Installation

1. **Install Python dependencies:**
   ```bash
   pip3 install pyaudio SpeechRecognition openai-whisper gtts pygame
   ```

2. **Install system dependencies (macOS):**
   ```bash
   brew install portaudio
   ```

3. **Load verses into database:**
   ```bash
   node versesLoader.mjs
   ```

## Usage

### Interactive Voice Chat
```bash
# Basic usage
python3 spiritual_chat.py

# With religion preference
python3 spiritual_chat.py --religion Hinduism

# With different Whisper model
python3 spiritual_chat.py --model small
```

### Test Without Voice
```bash
python3 test_spiritual_chat.py
```

## Example Queries

- "I need guidance about work"
- "Tell me about love"
- "I'm feeling anxious"
- "Help me with forgiveness"
- "Tell me about duty" (finds Bhagavad Gita)
- "I need peace"

## Supported Topics

- **Marriage & Love**: marriage, love, relationships
- **Work & Duty**: job, career, calling, duties
- **Stress & Anxiety**: worry, peace, comfort, rest
- **Fear & Courage**: fear, strength, bravery
- **Forgiveness**: mercy, grace, redemption
- **Hope & Faith**: hope, future, promises
- **Wisdom**: guidance, understanding, decisions
- **Healing**: health, recovery, medicine
- **Gratitude**: thankfulness, blessings
- **Death & Eternal**: grief, loss, eternal life

## Architecture

```
spiritual_chat.py
├── SpiritualChat class
│   ├── Speech Recognition (Google + Whisper fallback)
│   ├── Topic Detection (keyword matching)
│   ├── Database Search (SQLite)
│   └── Text-to-Speech (gTTS + pygame)
```

## Database Schema

```sql
CREATE TABLE verses (
  id TEXT PRIMARY KEY,
  religion TEXT,
  book TEXT,
  chapter INT,
  verse INT,
  text TEXT
);
```

## Command Line Options

```bash
python3 spiritual_chat.py --help

Options:
  --religion {Christianity,Islam,Judaism,Hinduism,Buddhism}
                        Preferred religion
  --model {tiny,base,small,medium,large}
                        Whisper model size (default: base)
  --db PATH            Path to SQLite database (default: verses.db)
```

## Troubleshooting

### Audio Issues
- **macOS**: Install PortAudio with `brew install portaudio`
- **Linux**: Install `sudo apt-get install portaudio19-dev`
- **Windows**: PyAudio should work out of the box

### Speech Recognition
- **Primary**: Google Speech Recognition (requires internet)
- **Fallback**: Whisper (works offline, downloads model first time)
- **Certificate Issues**: Whisper may have SSL issues, but Google SR still works

### No Verses Found
- Make sure database is loaded: `node versesLoader.mjs`
- Check database: `sqlite3 verses.db "SELECT COUNT(*) FROM verses;"`
- Try broader terms: "love", "peace", "hope"

## Performance

- **Whisper Models**:
  - `tiny`: Fastest, less accurate
  - `base`: Good balance (default)
  - `small`: Better accuracy
  - `medium/large`: Best accuracy, slower

- **Response Time**: 2-5 seconds typical
- **Database**: Instant search with SQLite

## Extending

### Add New Topics
Edit `topic_keywords` in `spiritual_chat.py`:

```python
self.topic_keywords = {
    "new_topic": ["keyword1", "keyword2", "keyword3"],
    # ... existing topics
}
```

### Add New Verses
Add JSON files to `data/` directory and run `node versesLoader.mjs`

## License

Open source - feel free to modify and distribute!
