#!/usr/bin/env python3
"""
Test script for the Spiritual Chat application
"""

from spiritual_chat import SpiritualC<PERSON>

def test_text_queries():
    """Test the application with text queries (no voice)."""
    print("🧪 Testing Spiritual Chat with text queries...\n")
    
    # Initialize the chat application
    chat = SpiritualChat()
    
    # Test queries
    test_queries = [
        ("Tell me about duty", "Hinduism"),
        ("I need guidance about work", None),
        ("Help me with love", "Christianity"),
        ("I'm feeling anxious", None),
        ("Tell me about righteousness", "Hinduism"),
        ("I need peace", None)
    ]
    
    for query, religion in test_queries:
        print(f"🔍 Query: '{query}'" + (f" (Religion: {religion})" if religion else ""))
        
        # Find verse
        verse = chat.find_verse(query, religion)
        
        # Create response
        response = chat.create_response(verse)
        
        print(f"📖 Response: {response}")
        
        if verse:
            print(f"   📚 Source: {verse['book']} ({verse['religion']})")
            if verse['topic']:
                print(f"   🎯 Topic: {verse['topic']} (keyword: {verse['keyword']})")
        
        print("-" * 60)

def test_database_stats():
    """Test database connectivity and show stats."""
    print("📊 Database Statistics:")
    
    import sqlite3
    try:
        conn = sqlite3.connect("verses.db")
        cursor = conn.cursor()
        
        # Total verses
        cursor.execute("SELECT COUNT(*) FROM verses")
        total = cursor.fetchone()[0]
        print(f"   Total verses: {total}")
        
        # By religion
        cursor.execute("SELECT religion, COUNT(*) FROM verses GROUP BY religion")
        religions = cursor.fetchall()
        for religion, count in religions:
            print(f"   {religion}: {count} verses")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == "__main__":
    print("🙏 Spiritual Chat Test Suite\n")
    
    # Test database
    test_database_stats()
    print()
    
    # Test text queries
    test_text_queries()
    
    print("\n✅ Testing completed!")
    print("\n💡 To run the full voice chat, use: python3 spiritual_chat.py")
    print("💡 For religion preference: python3 spiritual_chat.py --religion Hinduism")
