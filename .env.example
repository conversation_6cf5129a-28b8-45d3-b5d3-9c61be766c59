# Google Cloud Configuration
# You need to set up Google Cloud Speech-to-Text and Text-to-Speech APIs
# and either set GOOGLE_APPLICATION_CREDENTIALS or use other authentication methods

# Option 1: Service Account Key File Path
GOOGLE_APPLICATION_CREDENTIALS=path/to/your/service-account-key.json

# Option 2: Or set these if using other auth methods
# GOOGLE_CLOUD_PROJECT=your-project-id

# Server Configuration
PORT=3000

# Optional: Database Configuration
# DATABASE_PATH=verses.db
