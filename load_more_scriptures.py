#!/usr/bin/env python3
"""
Additional Scripture Loader - Load more Bible and Hindu texts
"""

import requests
import sqlite3
import json
import time

def load_bible_verses():
    """Load key Bible verses using a different approach"""
    print("📖 Loading additional Bible verses...")
    
    # Key Bible verses for spiritual guidance
    bible_verses = [
        # Love and Relationships
        {"book": "1 Corinthians", "chapter": 13, "verse": 4, "text": "Love is patient, love is kind. It does not envy, it does not boast, it is not proud."},
        {"book": "1 Corinthians", "chapter": 13, "verse": 13, "text": "And now these three remain: faith, hope and love. But the greatest of these is love."},
        {"book": "Ephesians", "chapter": 4, "verse": 32, "text": "Be kind and compassionate to one another, forgiving each other, just as in Christ God forgave you."},
        
        # Peace and Comfort
        {"book": "Matthew", "chapter": 11, "verse": 28, "text": "Come to me, all you who are weary and burdened, and I will give you rest."},
        {"book": "Philippians", "chapter": 4, "verse": 6, "text": "Do not be anxious about anything, but in every situation, by prayer and petition, with thanksgiving, present your requests to God."},
        {"book": "Philippians", "chapter": 4, "verse": 7, "text": "And the peace of God, which transcends all understanding, will guard your hearts and your minds in Christ Jesus."},
        {"book": "Isaiah", "chapter": 26, "verse": 3, "text": "You will keep in perfect peace those whose minds are steadfast, because they trust in you."},
        
        # Strength and Courage
        {"book": "Joshua", "chapter": 1, "verse": 9, "text": "Have I not commanded you? Be strong and courageous. Do not be afraid; do not be discouraged, for the Lord your God will be with you wherever you go."},
        {"book": "Isaiah", "chapter": 40, "verse": 31, "text": "But those who hope in the Lord will renew their strength. They will soar on wings like eagles; they will run and not grow weary, they will walk and not be faint."},
        {"book": "2 Timothy", "chapter": 1, "verse": 7, "text": "For the Spirit God gave us does not make us timid, but gives us power, love and self-discipline."},
        
        # Wisdom and Guidance
        {"book": "Proverbs", "chapter": 3, "verse": 5, "text": "Trust in the Lord with all your heart and lean not on your own understanding."},
        {"book": "Proverbs", "chapter": 3, "verse": 6, "text": "In all your ways submit to him, and he will make your paths straight."},
        {"book": "James", "chapter": 1, "verse": 5, "text": "If any of you lacks wisdom, you should ask God, who gives generously to all without finding fault, and it will be given to you."},
        
        # Hope and Faith
        {"book": "Romans", "chapter": 8, "verse": 28, "text": "And we know that in all things God works for the good of those who love him, who have been called according to his purpose."},
        {"book": "Jeremiah", "chapter": 29, "verse": 11, "text": "For I know the plans I have for you, declares the Lord, plans to prosper you and not to harm you, plans to give you hope and a future."},
        {"book": "Hebrews", "chapter": 11, "verse": 1, "text": "Now faith is confidence in what we hope for and assurance about what we do not see."},
        
        # Work and Purpose
        {"book": "Colossians", "chapter": 3, "verse": 23, "text": "Whatever you do, work at it with all your heart, as working for the Lord, not for human masters."},
        {"book": "Ecclesiastes", "chapter": 3, "verse": 1, "text": "There is a time for everything, and a season for every activity under the heavens."},
        
        # Forgiveness
        {"book": "Matthew", "chapter": 6, "verse": 14, "text": "For if you forgive other people when they sin against you, your heavenly Father will also forgive you."},
        {"book": "Ephesians", "chapter": 4, "verse": 31, "text": "Get rid of all bitterness, rage and anger, brawling and slander, along with every form of malice."},
        
        # Gratitude and Joy
        {"book": "1 Thessalonians", "chapter": 5, "verse": 18, "text": "Give thanks in all circumstances; for this is God's will for you in Christ Jesus."},
        {"book": "Philippians", "chapter": 4, "verse": 4, "text": "Rejoice in the Lord always. I will say it again: Rejoice!"},
        
        # Creation and Purpose
        {"book": "Genesis", "chapter": 1, "verse": 27, "text": "So God created mankind in his own image, in the image of God he created them; male and female he created them."},
        {"book": "Psalm", "chapter": 139, "verse": 14, "text": "I praise you because I am fearfully and wonderfully made; your works are wonderful, I know that full well."},
        
        # Salvation and Eternal Life
        {"book": "Romans", "chapter": 6, "verse": 23, "text": "For the wages of sin is death, but the gift of God is eternal life in Christ Jesus our Lord."},
        {"book": "Romans", "chapter": 10, "verse": 9, "text": "If you declare with your mouth, 'Jesus is Lord,' and believe in your heart that God raised him from the dead, you will be saved."},
    ]
    
    conn = sqlite3.connect("verses.db")
    cursor = conn.cursor()
    
    for verse in bible_verses:
        verse_id = f"bible_{verse['book'].lower().replace(' ', '')}_{verse['chapter']}_{verse['verse']}"
        
        cursor.execute('''
            INSERT OR REPLACE INTO verses 
            (id, religion, book, chapter, verse, text, translation, language)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            verse_id,
            "Christianity",
            verse['book'],
            verse['chapter'],
            verse['verse'],
            verse['text'],
            "NIV",
            "en"
        ))
    
    conn.commit()
    conn.close()
    print(f"✅ Loaded {len(bible_verses)} additional Bible verses")

def load_hindu_texts():
    """Load additional Hindu scripture verses"""
    print("🕉️ Loading additional Hindu texts...")
    
    # Key verses from various Hindu texts
    hindu_verses = [
        # Bhagavad Gita - Key verses
        {"book": "Bhagavad Gita", "chapter": 2, "verse": 47, "text": "You have a right to perform your prescribed duties, but you are not entitled to the fruits of your actions."},
        {"book": "Bhagavad Gita", "chapter": 4, "verse": 7, "text": "Whenever righteousness declines and unrighteousness rises, I manifest Myself."},
        {"book": "Bhagavad Gita", "chapter": 18, "verse": 66, "text": "Abandon all varieties of religion and just surrender unto Me. I shall deliver you from all sinful reactions. Do not fear."},
        {"book": "Bhagavad Gita", "chapter": 9, "verse": 22, "text": "To those who are constantly devoted and who always remember Me with love, I give the understanding by which they can come to Me."},
        {"book": "Bhagavad Gita", "chapter": 6, "verse": 5, "text": "One must deliver himself with the help of his mind, and not degrade himself. The mind is the friend of the conditioned soul, and his enemy as well."},
        
        # Upanishads
        {"book": "Isha Upanishad", "chapter": 1, "verse": 1, "text": "The entire universe is pervaded by the Lord. Enjoy life by renouncing the world. Do not covet anyone's wealth."},
        {"book": "Katha Upanishad", "chapter": 1, "verse": 3, "text": "When all the desires that dwell in the heart are cast away, then the mortal becomes immortal and attains Brahman even in this life."},
        
        # Vedas
        {"book": "Rig Veda", "chapter": 1, "verse": 164, "text": "Truth is one; sages call it by various names."},
        {"book": "Sama Veda", "chapter": 1, "verse": 1, "text": "May we be united in our resolve. May we be united in our understanding. May our minds be in harmony."},
        
        # Ramayana
        {"book": "Ramayana", "chapter": 1, "verse": 1, "text": "Dharma exists for the welfare of all beings. Hence, I must abandon my personal desires and follow dharma."},
        
        # Mahabharata
        {"book": "Mahabharata", "chapter": 1, "verse": 1, "text": "Dharma exists for the good of the world. Whatever is dharma is truth, and whatever is truth is dharma."},
    ]
    
    conn = sqlite3.connect("verses.db")
    cursor = conn.cursor()
    
    for verse in hindu_verses:
        verse_id = f"hindu_{verse['book'].lower().replace(' ', '')}_{verse['chapter']}_{verse['verse']}"
        
        cursor.execute('''
            INSERT OR REPLACE INTO verses 
            (id, religion, book, chapter, verse, text, translation, language)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            verse_id,
            "Hinduism",
            verse['book'],
            verse['chapter'],
            verse['verse'],
            verse['text'],
            "English",
            "en"
        ))
    
    conn.commit()
    conn.close()
    print(f"✅ Loaded {len(hindu_verses)} additional Hindu verses")

def load_buddhist_texts():
    """Load Buddhist scripture verses"""
    print("☸️ Loading Buddhist texts...")
    
    buddhist_verses = [
        # Dhammapada
        {"book": "Dhammapada", "chapter": 1, "verse": 1, "text": "All that we are is the result of what we have thought. The mind is everything. What we think we become."},
        {"book": "Dhammapada", "chapter": 1, "verse": 2, "text": "If a man speaks or acts with an evil thought, pain follows him. If a man speaks or acts with a pure thought, happiness follows him, like a shadow that never leaves him."},
        {"book": "Dhammapada", "chapter": 15, "verse": 197, "text": "Let us live happily then, not hating those who hate us! Among men who hate us let us dwell free from hatred!"},
        
        # Buddha's teachings
        {"book": "Buddha's Teachings", "chapter": 1, "verse": 1, "text": "Hatred does not cease by hatred, but only by love; this is the eternal rule."},
        {"book": "Buddha's Teachings", "chapter": 1, "verse": 2, "text": "Three things cannot be long hidden: the sun, the moon, and the truth."},
        {"book": "Buddha's Teachings", "chapter": 1, "verse": 3, "text": "Peace comes from within. Do not seek it without."},
        {"book": "Buddha's Teachings", "chapter": 1, "verse": 4, "text": "The mind is everything. What you think you become."},
        {"book": "Buddha's Teachings", "chapter": 1, "verse": 5, "text": "In the end, just three things matter: How much you loved, how gently you lived, and how gracefully you let go of things not meant for you."},
    ]
    
    conn = sqlite3.connect("verses.db")
    cursor = conn.cursor()
    
    for verse in buddhist_verses:
        verse_id = f"buddhist_{verse['book'].lower().replace(' ', '').replace("'", '')}_{verse['chapter']}_{verse['verse']}"
        
        cursor.execute('''
            INSERT OR REPLACE INTO verses 
            (id, religion, book, chapter, verse, text, translation, language)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            verse_id,
            "Buddhism",
            verse['book'],
            verse['chapter'],
            verse['verse'],
            verse['text'],
            "English",
            "en"
        ))
    
    conn.commit()
    conn.close()
    print(f"✅ Loaded {len(buddhist_verses)} Buddhist verses")

def get_final_stats():
    """Get final database statistics"""
    conn = sqlite3.connect("verses.db")
    cursor = conn.cursor()
    
    # Total verses
    cursor.execute("SELECT COUNT(*) FROM verses")
    total = cursor.fetchone()[0]
    
    # By religion
    cursor.execute("SELECT religion, COUNT(*) FROM verses GROUP BY religion ORDER BY COUNT(*) DESC")
    by_religion = cursor.fetchall()
    
    conn.close()
    
    print(f"\n📊 Final Database Statistics:")
    print(f"   Total verses: {total:,}")
    print(f"\n   By Religion:")
    for religion, count in by_religion:
        print(f"     {religion}: {count:,} verses")

def main():
    print("🙏 Loading additional scriptures...\n")
    
    load_bible_verses()
    load_hindu_texts()
    load_buddhist_texts()
    
    get_final_stats()
    
    print("\n✅ Additional scripture loading completed!")

if __name__ == "__main__":
    main()
