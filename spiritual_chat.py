#!/usr/bin/env python3
"""
Spiritual Chat Application - Python Version
Uses Whisper for speech recognition, gTTS for text-to-speech,
and SQLite database for verse storage.
"""

import whisper
import speech_recognition as sr
from gtts import gTTS
import pygame
import os
import sqlite3
import random
import tempfile
import time
from typing import Optional, Dict, List

class SpiritualChat:
    def __init__(self, db_path: str = "verses.db", model_size: str = "base"):
        """Initialize the spiritual chat application."""
        self.db_path = db_path
        self.model = None
        self.recognizer = sr.Recognizer()
        
        # Initialize pygame mixer for audio playback
        pygame.mixer.init()
        
        # Topic keywords for better matching
        self.topic_keywords = {
            "marriage": ["marriage", "husband", "wife", "union", "love", "wedding", "spouse", "relationship", "loved"],
            "stress": ["worry", "peace", "comfort", "rest", "trust", "anxiety", "calm", "burden", "overwhelmed", "anxious"],
            "work": ["job", "labor", "duty", "duties", "calling", "career", "employment", "workplace", "business", "prescribed", "perform"],
            "fear": ["fear", "courage", "strength", "anxiety", "afraid", "scared", "brave", "bold"],
            "forgiveness": ["forgive", "mercy", "grace", "pardon", "redemption", "sin", "guilt"],
            "hope": ["hope", "faith", "future", "promise", "expectation", "optimism", "righteousness"],
            "wisdom": ["wisdom", "knowledge", "understanding", "guidance", "decision", "choice", "shepherd"],
            "healing": ["heal", "sick", "health", "recovery", "medicine", "doctor", "illness"],
            "gratitude": ["thank", "grateful", "blessing", "appreciate", "praise"],
            "death": ["death", "dying", "grief", "loss", "mourning", "funeral", "eternal", "perish"]
        }
        
        print("🙏 Initializing Spiritual Chat...")
        self._load_whisper_model(model_size)
        self._check_database()
        print("✅ Ready to provide spiritual guidance!")

    def _load_whisper_model(self, model_size: str):
        """Load the Whisper model for speech recognition."""
        try:
            print(f"📥 Loading Whisper model ({model_size})...")
            self.model = whisper.load_model(model_size)
            print("✅ Whisper model loaded successfully")
        except Exception as e:
            print(f"❌ Error loading Whisper model: {e}")
            self.model = None

    def _check_database(self):
        """Check if the database exists and has verses."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM verses")
            count = cursor.fetchone()[0]
            conn.close()
            print(f"📚 Found {count} verses in database")
        except Exception as e:
            print(f"❌ Database error: {e}")
            print("💡 Make sure to run 'node versesLoader.mjs' first to load verses")

    def listen_microphone(self) -> Optional[str]:
        """Listen to microphone input and convert to text using SpeechRecognition."""
        try:
            mic = sr.Microphone()
            with mic as source:
                print("🎙️  Adjusting for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)
                print("🎙️  Speak now...")
                audio = self.recognizer.listen(source, timeout=10, phrase_time_limit=10)

            print("🔄 Processing speech...")
            
            # Try Google Speech Recognition first (free tier)
            try:
                text = self.recognizer.recognize_google(audio)
                print(f"🗣️  You said: {text}")
                return text
            except sr.UnknownValueError:
                print("❌ Could not understand audio")
                return None
            except sr.RequestError as e:
                print(f"⚠️  Google Speech Recognition error: {e}")
                # Fallback to Whisper if available
                return self._whisper_fallback(audio)
                
        except sr.WaitTimeoutError:
            print("⏰ No speech detected within timeout")
            return None
        except Exception as e:
            print(f"❌ Error during speech recognition: {e}")
            return None

    def _whisper_fallback(self, audio) -> Optional[str]:
        """Use Whisper as fallback for speech recognition."""
        if not self.model:
            print("❌ Whisper model not available")
            return None
            
        try:
            # Save audio to temporary file
            with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as temp_file:
                temp_file.write(audio.get_wav_data())
                temp_path = temp_file.name

            # Transcribe with Whisper
            result = self.model.transcribe(temp_path)
            text = result["text"].strip()
            
            # Clean up
            os.unlink(temp_path)
            
            if text:
                print(f"🗣️  You said (Whisper): {text}")
                return text
            else:
                print("❌ No speech detected")
                return None
                
        except Exception as e:
            print(f"❌ Whisper transcription error: {e}")
            return None

    def find_best_topic(self, query: str) -> Dict[str, str]:
        """Find the best matching topic and keyword."""
        query_lower = query.lower()
        best_match = {"topic": None, "keyword": None, "score": 0}
        
        for topic, keywords in self.topic_keywords.items():
            for keyword in keywords:
                if keyword in query_lower:
                    score = len(keyword)  # Longer keywords get higher scores
                    if score > best_match["score"]:
                        best_match = {"topic": topic, "keyword": keyword, "score": score}
        
        return best_match

    def find_verse(self, query: str, preferred_religion: str = None) -> Optional[Dict]:
        """Find a relevant verse from the database."""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Get topic match
            topic_match = self.find_best_topic(query)
            
            # Build search terms with better ordering
            search_terms = []

            # Add all keywords from the matched topic (not just the matched keyword)
            if topic_match["topic"]:
                topic_keywords = self.topic_keywords.get(topic_match["topic"], [])
                search_terms.extend(topic_keywords)

            # Add the specific matched keyword first if it exists
            if topic_match["keyword"] and topic_match["keyword"] not in search_terms:
                search_terms.insert(0, topic_match["keyword"])

            # Add individual words from query (longer than 3 chars)
            query_words = [word for word in query.lower().split() if len(word) > 3]
            for word in query_words:
                if word not in search_terms:
                    search_terms.append(word)
            
            # Try to find verses with different search strategies
            for term in search_terms:
                if preferred_religion:
                    cursor.execute(
                        "SELECT * FROM verses WHERE text LIKE ? AND religion = ? ORDER BY RANDOM() LIMIT 1",
                        (f"%{term}%", preferred_religion)
                    )
                else:
                    cursor.execute(
                        "SELECT * FROM verses WHERE text LIKE ? ORDER BY RANDOM() LIMIT 1",
                        (f"%{term}%",)
                    )
                
                result = cursor.fetchone()
                if result:
                    conn.close()
                    return {
                        "id": result[0],
                        "religion": result[1],
                        "book": result[2],
                        "chapter": result[3],
                        "verse": result[4],
                        "text": result[5],
                        "topic": topic_match["topic"],
                        "keyword": topic_match["keyword"]
                    }
            
            conn.close()
            return None
            
        except Exception as e:
            print(f"❌ Database query error: {e}")
            return None

    def create_response(self, verse: Optional[Dict]) -> str:
        """Create a response message."""
        if verse:
            chapter_verse = ""
            if verse["chapter"] and verse["verse"]:
                chapter_verse = f" chapter {verse['chapter']} verse {verse['verse']}"
            elif verse["chapter"]:
                chapter_verse = f" chapter {verse['chapter']}"
            elif verse["verse"]:
                chapter_verse = f" verse {verse['verse']}"
            
            return f"From {verse['book']}{chapter_verse}, a sacred text in {verse['religion']}: \"{verse['text']}\""
        else:
            return "I'm still learning these scriptures. Could you ask about topics like love, peace, wisdom, hope, or forgiveness?"

    def speak(self, text: str):
        """Convert text to speech and play it."""
        try:
            print("🔊 Generating speech...")
            tts = gTTS(text=text, lang='en', slow=False)
            
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".mp3", delete=False) as temp_file:
                temp_path = temp_file.name
                tts.save(temp_path)
            
            # Play audio using pygame
            pygame.mixer.music.load(temp_path)
            pygame.mixer.music.play()
            
            # Wait for playback to finish
            while pygame.mixer.music.get_busy():
                time.sleep(0.1)
            
            # Clean up
            os.unlink(temp_path)
            print("✅ Speech playback completed")
            
        except Exception as e:
            print(f"❌ Text-to-speech error: {e}")
            print(f"📖 Response: {text}")

    def chat_session(self, preferred_religion: str = None):
        """Run an interactive chat session."""
        print("\n🙏 Welcome to Spiritual Chat!")
        print("💬 Say something like:")
        print("   • 'I need guidance about work'")
        print("   • 'Tell me about love'")
        print("   • 'I'm feeling anxious'")
        print("   • 'Help me with forgiveness'")
        if preferred_religion:
            print(f"🕊️  Preferred religion: {preferred_religion}")
        print("🛑 Say 'quit' or 'exit' to end the session\n")
        
        while True:
            try:
                # Listen for user input
                user_input = self.listen_microphone()
                
                if not user_input:
                    print("🔄 Please try speaking again...")
                    continue
                
                # Check for exit commands
                if user_input.lower().strip() in ['quit', 'exit', 'stop', 'bye']:
                    print("🙏 Thank you for using Spiritual Chat. May you find peace!")
                    break
                
                # Find relevant verse
                verse = self.find_verse(user_input, preferred_religion)
                
                # Create and deliver response
                response = self.create_response(verse)
                print(f"📖 Response: {response}")
                
                # Speak the response
                self.speak(response)
                
                print("\n" + "="*50 + "\n")
                
            except KeyboardInterrupt:
                print("\n🙏 Session ended. Peace be with you!")
                break
            except Exception as e:
                print(f"❌ Error during chat session: {e}")
                continue

def main():
    """Main function to run the spiritual chat application."""
    import argparse
    
    parser = argparse.ArgumentParser(description="Spiritual Chat - Voice-enabled spiritual guidance")
    parser.add_argument("--religion", type=str, help="Preferred religion (Christianity, Islam, Judaism, Hinduism, Buddhism)")
    parser.add_argument("--model", type=str, default="base", choices=["tiny", "base", "small", "medium", "large"], 
                       help="Whisper model size (default: base)")
    parser.add_argument("--db", type=str, default="verses.db", help="Path to SQLite database")
    
    args = parser.parse_args()
    
    # Create and run the chat application
    chat = SpiritualChat(db_path=args.db, model_size=args.model)
    chat.chat_session(preferred_religion=args.religion)

if __name__ == "__main__":
    main()
