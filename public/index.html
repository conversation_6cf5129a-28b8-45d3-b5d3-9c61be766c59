<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Spiritual Voice Chat</title>
    <style>
        body {
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .controls {
            display: flex;
            flex-direction: column;
            gap: 20px;
            margin-bottom: 30px;
        }
        .religion-select {
            padding: 12px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-size: 16px;
        }
        .religion-select option {
            background: #333;
            color: white;
        }
        .record-btn {
            padding: 20px 40px;
            font-size: 18px;
            border: none;
            border-radius: 50px;
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .record-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        .record-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }
        .chat-log {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
            margin-bottom: 20px;
        }
        .message {
            margin-bottom: 15px;
            padding: 10px 15px;
            border-radius: 10px;
        }
        .user-message {
            background: rgba(100, 200, 255, 0.3);
            margin-left: 20px;
        }
        .bot-message {
            background: rgba(255, 255, 255, 0.2);
            margin-right: 20px;
        }
        .verse-info {
            font-size: 0.9em;
            opacity: 0.8;
            margin-top: 5px;
        }
        .status {
            text-align: center;
            padding: 10px;
            border-radius: 10px;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
        }
        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }
        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid white;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🙏 Spiritual Voice Chat</h1>
        
        <div class="controls">
            <select class="religion-select" id="religionSelect">
                <option value="">Any Religion</option>
                <option value="Christianity">Christianity</option>
                <option value="Islam">Islam</option>
                <option value="Judaism">Judaism</option>
                <option value="Buddhism">Buddhism</option>
                <option value="Hinduism">Hinduism</option>
            </select>
            
            <button class="record-btn" id="recordBtn">🎤 Hold to Speak</button>
        </div>

        <div class="status" id="status">Ready to listen. Hold the button and speak about topics like love, peace, wisdom, or hope.</div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            Processing your message...
        </div>

        <div class="chat-log" id="chatLog"></div>
    </div>

    <script>
        let mediaRecorder;
        let audioChunks = [];
        let isRecording = false;

        const recordBtn = document.getElementById('recordBtn');
        const chatLog = document.getElementById('chatLog');
        const status = document.getElementById('status');
        const loading = document.getElementById('loading');
        const religionSelect = document.getElementById('religionSelect');

        // Initialize audio recording
        async function initAudio() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                mediaRecorder = new MediaRecorder(stream, { mimeType: 'audio/webm;codecs=opus' });
                
                mediaRecorder.ondataavailable = (event) => {
                    audioChunks.push(event.data);
                };
                
                mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(audioChunks, { type: 'audio/webm;codecs=opus' });
                    audioChunks = [];
                    await sendAudio(audioBlob);
                };
                
                status.textContent = "Ready! Hold the button and speak.";
            } catch (error) {
                status.textContent = "Microphone access denied. Please allow microphone access.";
                console.error('Error accessing microphone:', error);
            }
        }

        // Convert blob to base64
        function blobToBase64(blob) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onloadend = () => resolve(reader.result.split(',')[1]);
                reader.readAsDataURL(blob);
            });
        }

        // Send audio to server
        async function sendAudio(audioBlob) {
            try {
                loading.style.display = 'block';
                status.style.display = 'none';
                
                const audioBase64 = await blobToBase64(audioBlob);
                
                const response = await fetch('/chat', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ 
                        audioContentBase64: audioBase64,
                        preferredReligion: religionSelect.value || null
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    displayMessage(data.userSaid, 'user');
                    displayMessage(data.replyText, 'bot', data.verse);
                    
                    // Play response audio
                    if (data.audioContentBase64) {
                        playAudio(data.audioContentBase64);
                    }
                } else {
                    status.textContent = `Error: ${data.error}`;
                }
                
            } catch (error) {
                console.error('Error sending audio:', error);
                status.textContent = 'Error processing your message. Please try again.';
            } finally {
                loading.style.display = 'none';
                status.style.display = 'block';
                status.textContent = "Ready! Hold the button and speak.";
            }
        }

        // Display message in chat log
        function displayMessage(text, sender, verse = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${sender}-message`;
            
            let content = `<div>${text}</div>`;
            if (verse && sender === 'bot') {
                content += `<div class="verse-info">📖 ${verse.book} ${verse.chapter ? `${verse.chapter}:${verse.verse}` : ''} (${verse.religion})</div>`;
            }
            
            messageDiv.innerHTML = content;
            chatLog.appendChild(messageDiv);
            chatLog.scrollTop = chatLog.scrollHeight;
        }

        // Play audio response
        function playAudio(base64Audio) {
            const audio = new Audio(`data:audio/mp3;base64,${base64Audio}`);
            audio.play().catch(console.error);
        }

        // Event listeners
        recordBtn.addEventListener('mousedown', startRecording);
        recordBtn.addEventListener('mouseup', stopRecording);
        recordBtn.addEventListener('mouseleave', stopRecording);
        
        // Touch events for mobile
        recordBtn.addEventListener('touchstart', startRecording);
        recordBtn.addEventListener('touchend', stopRecording);

        function startRecording() {
            if (!mediaRecorder || isRecording) return;
            
            isRecording = true;
            audioChunks = [];
            mediaRecorder.start();
            recordBtn.textContent = '🔴 Recording...';
            recordBtn.style.background = 'linear-gradient(45deg, #ff4757, #c44569)';
            status.textContent = 'Listening... Release to send.';
        }

        function stopRecording() {
            if (!mediaRecorder || !isRecording) return;
            
            isRecording = false;
            mediaRecorder.stop();
            recordBtn.textContent = '🎤 Hold to Speak';
            recordBtn.style.background = 'linear-gradient(45deg, #ff6b6b, #ee5a24)';
            status.textContent = 'Processing...';
        }

        // Initialize when page loads
        initAudio();
    </script>
</body>
</html>
