#!/usr/bin/env python3
"""
Comprehensive Scripture Data Loader
Downloads and loads verses from multiple religious texts into SQLite database.
"""

import requests
import sqlite3
import json
import time
from typing import List, Dict, Optional
import os

class ScriptureLoader:
    def __init__(self, db_path: str = "verses.db"):
        self.db_path = db_path
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'SpiritualChat/1.0 (Educational Purpose)'
        })
        
    def init_database(self):
        """Initialize the database with proper schema."""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Check if table exists and get its schema
        cursor.execute("PRAGMA table_info(verses)")
        columns = [row[1] for row in cursor.fetchall()]

        if not columns:
            # Create new table
            cursor.execute('''
                CREATE TABLE verses (
                    id TEXT PRIMARY KEY,
                    religion TEXT,
                    book TEXT,
                    chapter INTEGER,
                    verse INTEGER,
                    text TEXT,
                    translation TEXT DEFAULT NULL,
                    language TEXT DEFAULT 'en'
                )
            ''')
            print("✅ Created new verses table")
        else:
            # Add missing columns if needed
            if 'translation' not in columns:
                cursor.execute('ALTER TABLE verses ADD COLUMN translation TEXT DEFAULT NULL')
                print("✅ Added translation column")
            if 'language' not in columns:
                cursor.execute('ALTER TABLE verses ADD COLUMN language TEXT DEFAULT "en"')
                print("✅ Added language column")
            print("✅ Database schema updated")

        conn.commit()
        conn.close()

    def load_bible_kjv(self):
        """Load King James Version Bible from bible-api.com"""
        print("📖 Loading Bible (KJV)...")
        
        # Bible books in order
        bible_books = [
            # Old Testament
            "Genesis", "Exodus", "Leviticus", "Numbers", "Deuteronomy",
            "Joshua", "Judges", "Ruth", "1 Samuel", "2 Samuel", "1 Kings", "2 Kings",
            "1 Chronicles", "2 Chronicles", "Ezra", "Nehemiah", "Esther",
            "Job", "Psalms", "Proverbs", "Ecclesiastes", "Song of Solomon",
            "Isaiah", "Jeremiah", "Lamentations", "Ezekiel", "Daniel",
            "Hosea", "Joel", "Amos", "Obadiah", "Jonah", "Micah", "Nahum",
            "Habakkuk", "Zephaniah", "Haggai", "Zechariah", "Malachi",
            # New Testament
            "Matthew", "Mark", "Luke", "John", "Acts", "Romans",
            "1 Corinthians", "2 Corinthians", "Galatians", "Ephesians",
            "Philippians", "Colossians", "1 Thessalonians", "2 Thessalonians",
            "1 Timothy", "2 Timothy", "Titus", "Philemon", "Hebrews",
            "James", "1 Peter", "2 Peter", "1 John", "2 John", "3 John",
            "Jude", "Revelation"
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        total_verses = 0
        
        for book in bible_books[:5]:  # Start with first 5 books for testing
            print(f"  📚 Loading {book}...")
            
            try:
                # Get the entire book
                url = f"https://bible-api.com/{book}"
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Parse verses
                    for verse_data in data.get('verses', []):
                        verse_id = f"bible_kjv_{book.lower().replace(' ', '')}_{verse_data['chapter']}_{verse_data['verse']}"
                        
                        cursor.execute('''
                            INSERT OR REPLACE INTO verses 
                            (id, religion, book, chapter, verse, text, translation, language)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            verse_id,
                            "Christianity",
                            book,
                            verse_data['chapter'],
                            verse_data['verse'],
                            verse_data['text'],
                            "KJV",
                            "en"
                        ))
                        total_verses += 1
                    
                    time.sleep(0.5)  # Be respectful to the API
                    
                else:
                    print(f"    ❌ Failed to load {book}: {response.status_code}")
                    
            except Exception as e:
                print(f"    ❌ Error loading {book}: {e}")
                continue
        
        conn.commit()
        conn.close()
        print(f"✅ Loaded {total_verses} Bible verses")

    def load_quran(self):
        """Load Quran from alquran.cloud API"""
        print("🕌 Loading Quran...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        total_verses = 0
        
        try:
            # Get all surahs
            url = "https://api.alquran.cloud/v1/quran/en.asad"
            response = self.session.get(url, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                for surah in data['data']['surahs']:
                    surah_number = surah['number']
                    surah_name = surah['englishName']
                    
                    print(f"  📚 Loading Surah {surah_number}: {surah_name}")
                    
                    for ayah in surah['ayahs']:
                        verse_id = f"quran_{surah_number}_{ayah['numberInSurah']}"
                        
                        cursor.execute('''
                            INSERT OR REPLACE INTO verses 
                            (id, religion, book, chapter, verse, text, translation, language)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            verse_id,
                            "Islam",
                            surah_name,
                            surah_number,
                            ayah['numberInSurah'],
                            ayah['text'],
                            "Muhammad Asad",
                            "en"
                        ))
                        total_verses += 1
                
                conn.commit()
                print(f"✅ Loaded {total_verses} Quran verses")
                
            else:
                print(f"❌ Failed to load Quran: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error loading Quran: {e}")
        
        conn.close()

    def load_bhagavad_gita(self):
        """Load Bhagavad Gita from bhagavadgitaapi.in"""
        print("🕉️ Loading Bhagavad Gita...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        total_verses = 0
        
        try:
            # Get all chapters (1-18)
            for chapter in range(1, 19):
                print(f"  📚 Loading Chapter {chapter}...")
                
                url = f"https://bhagavadgitaapi.in/chapter/{chapter}/"
                response = self.session.get(url, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    # Get verses for this chapter
                    for verse_num in range(1, data.get('verses_count', 0) + 1):
                        verse_url = f"https://bhagavadgitaapi.in/slok/{chapter}/{verse_num}/"
                        verse_response = self.session.get(verse_url, timeout=10)
                        
                        if verse_response.status_code == 200:
                            verse_data = verse_response.json()
                            
                            verse_id = f"gita_{chapter}_{verse_num}"
                            
                            # Use English translation
                            text = verse_data.get('tej', {}).get('et', '') or verse_data.get('siva', {}).get('et', '')
                            
                            if text:
                                cursor.execute('''
                                    INSERT OR REPLACE INTO verses 
                                    (id, religion, book, chapter, verse, text, translation, language)
                                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                                ''', (
                                    verse_id,
                                    "Hinduism",
                                    "Bhagavad Gita",
                                    chapter,
                                    verse_num,
                                    text,
                                    "English",
                                    "en"
                                ))
                                total_verses += 1
                        
                        time.sleep(0.2)  # Be respectful
                
                time.sleep(0.5)
        
        except Exception as e:
            print(f"❌ Error loading Bhagavad Gita: {e}")
        
        conn.commit()
        conn.close()
        print(f"✅ Loaded {total_verses} Bhagavad Gita verses")

    def load_sample_torah(self):
        """Load sample Torah verses (since we don't have a free comprehensive API)"""
        print("✡️ Loading Torah (sample verses)...")
        
        # Sample Torah verses for key topics
        torah_verses = [
            {
                "book": "Genesis",
                "chapter": 1,
                "verse": 1,
                "text": "In the beginning God created the heavens and the earth."
            },
            {
                "book": "Genesis",
                "chapter": 1,
                "verse": 27,
                "text": "So God created mankind in his own image, in the image of God he created them; male and female he created them."
            },
            {
                "book": "Exodus",
                "chapter": 20,
                "verse": 3,
                "text": "You shall have no other gods before me."
            },
            {
                "book": "Deuteronomy",
                "chapter": 6,
                "verse": 4,
                "text": "Hear, O Israel: The Lord our God, the Lord is one."
            },
            {
                "book": "Leviticus",
                "chapter": 19,
                "verse": 18,
                "text": "Do not seek revenge or bear a grudge against anyone among your people, but love your neighbor as yourself. I am the Lord."
            },
            {
                "book": "Psalms",
                "chapter": 23,
                "verse": 1,
                "text": "The Lord is my shepherd, I lack nothing."
            },
            {
                "book": "Proverbs",
                "chapter": 3,
                "verse": 5,
                "text": "Trust in the Lord with all your heart and lean not on your own understanding."
            },
            {
                "book": "Ecclesiastes",
                "chapter": 3,
                "verse": 1,
                "text": "There is a time for everything, and a season for every activity under the heavens."
            }
        ]
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        for verse in torah_verses:
            verse_id = f"torah_{verse['book'].lower()}_{verse['chapter']}_{verse['verse']}"
            
            cursor.execute('''
                INSERT OR REPLACE INTO verses 
                (id, religion, book, chapter, verse, text, translation, language)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                verse_id,
                "Judaism",
                verse['book'],
                verse['chapter'],
                verse['verse'],
                verse['text'],
                "Hebrew Bible",
                "en"
            ))
        
        conn.commit()
        conn.close()
        print(f"✅ Loaded {len(torah_verses)} Torah verses")

    def get_database_stats(self):
        """Get statistics about loaded verses"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # Total verses
        cursor.execute("SELECT COUNT(*) FROM verses")
        total = cursor.fetchone()[0]
        
        # By religion
        cursor.execute("SELECT religion, COUNT(*) FROM verses GROUP BY religion ORDER BY COUNT(*) DESC")
        by_religion = cursor.fetchall()
        
        # By book (top 10)
        cursor.execute("SELECT book, COUNT(*) FROM verses GROUP BY book ORDER BY COUNT(*) DESC LIMIT 10")
        by_book = cursor.fetchall()
        
        conn.close()
        
        print(f"\n📊 Database Statistics:")
        print(f"   Total verses: {total:,}")
        print(f"\n   By Religion:")
        for religion, count in by_religion:
            print(f"     {religion}: {count:,} verses")
        print(f"\n   Top Books:")
        for book, count in by_book:
            print(f"     {book}: {count:,} verses")

    def load_all(self):
        """Load all available scriptures"""
        print("🙏 Starting comprehensive scripture loading...\n")
        
        self.init_database()
        
        # Load each source
        self.load_sample_torah()
        self.load_bhagavad_gita()
        self.load_bible_kjv()
        self.load_quran()
        
        # Show final stats
        self.get_database_stats()
        
        print("\n✅ Scripture loading completed!")

def main():
    loader = ScriptureLoader()
    loader.load_all()

if __name__ == "__main__":
    main()
