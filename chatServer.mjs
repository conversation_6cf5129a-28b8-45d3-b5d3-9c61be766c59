import 'dotenv/config';
import express from "express";
import fs from "fs/promises";
import {SpeechClient} from "@google-cloud/speech";
import {TextToSpeechClient} from "@google-cloud/text-to-speech";
import sqlite3 from "sqlite3";

// Initialize Google Cloud clients with error handling
let stt, tts;
let googleCloudEnabled = false;

try {
  stt = new SpeechClient();
  tts = new TextToSpeechClient();
  googleCloudEnabled = true;
  console.log("✅ Google Cloud Speech APIs initialized");
} catch (error) {
  console.log("⚠️  Google Cloud credentials not found. Voice features will be limited.");
  console.log("   Set up credentials to enable full voice functionality.");
}

const db = new sqlite3.Database("verses.db");
const app = express();

// Middleware
app.use(express.json({limit:"10mb"}));
app.use(express.static('public')); // Serve static files

// Enhanced keyword mapping with more comprehensive topics
const topicKeywords = {
  "marriage": ["marriage", "husband", "wife", "union", "love", "wedding", "spouse", "relationship"],
  "stress": ["worry", "peace", "comfort", "rest", "trust", "anxiety", "calm", "burden", "overwhelmed"],
  "work": ["job", "labor", "duty", "calling", "career", "employment", "workplace", "business"],
  "fear": ["fear", "courage", "strength", "anxiety", "afraid", "scared", "brave", "bold"],
  "forgiveness": ["forgive", "mercy", "grace", "pardon", "redemption", "sin", "guilt"],
  "hope": ["hope", "faith", "future", "promise", "expectation", "optimism"],
  "wisdom": ["wisdom", "knowledge", "understanding", "guidance", "decision", "choice"],
  "healing": ["heal", "sick", "health", "recovery", "medicine", "doctor", "illness"],
  "gratitude": ["thank", "grateful", "blessing", "appreciate", "praise"],
  "death": ["death", "dying", "grief", "loss", "mourning", "funeral", "eternal"]
};

// Helper function to find the best matching topic
function findBestTopic(userText) {
  const lowerText = userText.toLowerCase();
  let bestMatch = { topic: null, keyword: null, score: 0 };

  for (const [topic, keywords] of Object.entries(topicKeywords)) {
    for (const keyword of keywords) {
      if (lowerText.includes(keyword)) {
        // Give higher score to longer keyword matches
        const score = keyword.length;
        if (score > bestMatch.score) {
          bestMatch = { topic, keyword, score };
        }
      }
    }
  }

  return bestMatch;
}

// Helper function to get verse with better search logic
function getRelevantVerse(searchTerm, religion = null) {
  return new Promise((resolve, reject) => {
    let query = `SELECT * FROM verses WHERE text LIKE ?`;
    let params = [`%${searchTerm}%`];

    if (religion) {
      query += ` AND religion = ?`;
      params.push(religion);
    }

    query += ` ORDER BY RANDOM() LIMIT 1`;

    db.get(query, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row);
      }
    });
  });
}

app.post("/chat", async (req, res) => {
  try {
    const { audioContentBase64, preferredReligion, textInput } = req.body;

    let userText = "";

    // Check if we have text input (for testing without voice)
    if (textInput) {
      userText = textInput;
      console.log("💬 User typed:", userText);
    } else if (audioContentBase64 && googleCloudEnabled) {
      // STEP 1: Convert speech to text (only if Google Cloud is available)
      const [sttResp] = await stt.recognize({
        audio: { content: audioContentBase64 },
        config: {
          encoding: "WEBM_OPUS",
          sampleRateHertz: 48000,
          languageCode: "en-US",
          enableAutomaticPunctuation: true,
          model: "latest_long"
        }
      });

      userText = sttResp.results?.[0]?.alternatives?.[0]?.transcript || "";
      console.log("🗣️ User said:", userText);
    } else if (audioContentBase64 && !googleCloudEnabled) {
      return res.status(503).json({
        error: "Voice recognition not available",
        message: "Google Cloud credentials not configured. Please set up authentication or use text input for testing."
      });
    } else {
      return res.status(400).json({ error: "Audio content or text input is required" });
    }

    if (!userText.trim()) {
      return res.json({
        userSaid: "",
        replyText: "I didn't catch that. Could you please speak again?",
        topic: null,
        audioContentBase64: null
      });
    }

    // STEP 2: Detect topic with improved matching
    const topicMatch = findBestTopic(userText);
    console.log("🎯 Topic detected:", topicMatch.topic, "via keyword:", topicMatch.keyword);

    // STEP 3: Get verse with fallback strategy
    let verse = null;
    const searchTerms = [
      topicMatch.keyword,
      topicMatch.topic,
      ...userText.toLowerCase().split(" ").filter(word => word.length > 3)
    ].filter(Boolean);

    // Try to find a verse using different search terms
    for (const term of searchTerms) {
      verse = await getRelevantVerse(term, preferredReligion);
      if (verse) break;
    }

    // If no verse found with preferred religion, try without religion filter
    if (!verse && preferredReligion) {
      for (const term of searchTerms) {
        verse = await getRelevantVerse(term);
        if (verse) break;
      }
    }

    // STEP 4: Prepare response
    const fallbackReply = "I'm still learning these scriptures. Could you ask about topics like love, peace, wisdom, or hope?";
    const replyText = verse
      ? `From ${verse.book} ${verse.chapter ? `chapter ${verse.chapter}` : ''} ${verse.verse ? `verse ${verse.verse}` : ''}, a sacred text in ${verse.religion}: "${verse.text}"`
      : fallbackReply;

    console.log("📖 Reply:", replyText);

    // STEP 5: Convert reply to speech (only if Google Cloud is available)
    let audioContentBase64 = null;
    if (googleCloudEnabled) {
      try {
        const [ttsResp] = await tts.synthesizeSpeech({
          input: { text: replyText },
          voice: {
            languageCode: "en-US",
            name: "en-US-Standard-C",
            ssmlGender: "FEMALE"
          },
          audioConfig: {
            audioEncoding: "MP3",
            speakingRate: 0.9,
            pitch: 0.0
          }
        });
        audioContentBase64 = ttsResp.audioContent.toString("base64");
      } catch (error) {
        console.log("⚠️  TTS failed:", error.message);
      }
    }

    res.json({
      userSaid: userText,
      replyText,
      topic: topicMatch.topic,
      keyword: topicMatch.keyword,
      verse: verse ? {
        book: verse.book,
        chapter: verse.chapter,
        verse: verse.verse,
        religion: verse.religion
      } : null,
      audioContentBase64,
      googleCloudEnabled
    });

  } catch (error) {
    console.error("❌ Error in /chat:", error);
    res.status(500).json({
      error: "Internal server error",
      message: error.message
    });
  }
});

// Health check endpoint
app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Get available topics
app.get("/topics", (req, res) => {
  res.json({
    topics: Object.keys(topicKeywords),
    topicKeywords
  });
});

// Get verse count by religion
app.get("/stats", (req, res) => {
  db.all(`SELECT religion, COUNT(*) as count FROM verses GROUP BY religion`, (err, rows) => {
    if (err) {
      res.status(500).json({ error: err.message });
    } else {
      res.json({ verseStats: rows });
    }
  });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`✨ Spiritual Voice Chat running on http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/health`);
  console.log(`📚 Topics: http://localhost:${PORT}/topics`);
  console.log(`📈 Stats: http://localhost:${PORT}/stats`);
});