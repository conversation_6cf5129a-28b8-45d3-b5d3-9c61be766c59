import sqlite3 from "sqlite3";
import fs from "fs/promises";
import path from "path";

const db = new sqlite3.Database("verses.db");

async function loadVerses() {
  try {
    // Check if data directory exists
    const dataDir = "data";
    try {
      await fs.access(dataDir);
    } catch (error) {
      console.log(`❌ Data directory '${dataDir}' not found`);
      return;
    }

    const files = await fs.readdir(dataDir);
    const jsonFiles = files.filter(file => file.endsWith(".json"));

    if (jsonFiles.length === 0) {
      console.log("📁 No JSON files found in data directory");
      return;
    }

    console.log(`📚 Found ${jsonFiles.length} JSON file(s) to process`);

    for (const file of jsonFiles) {
      try {
        const filePath = path.join(dataDir, file);
        const content = await fs.readFile(filePath, "utf-8");
        const verses = JSON.parse(content);

        if (!Array.isArray(verses)) {
          console.log(`⚠️  Skipping ${file}: Expected array of verses`);
          continue;
        }

        let insertedCount = 0;
        for (const v of verses) {
          // Validate verse structure
          if (!v.id || !v.religion || !v.book || !v.text) {
            console.log(`⚠️  Skipping invalid verse in ${file}:`, v);
            continue;
          }

          await new Promise((resolve, reject) => {
            db.run(`INSERT OR IGNORE INTO verses (id, religion, book, chapter, verse, text)
                    VALUES (?, ?, ?, ?, ?, ?)`,
              [v.id, v.religion, v.book, v.chapter || null, v.verse || null, v.text],
              function(err) {
                if (err) {
                  reject(err);
                } else {
                  if (this.changes > 0) insertedCount++;
                  resolve();
                }
              });
          });
        }

        console.log(`✅ Loaded ${file}: ${insertedCount} new verses inserted (${verses.length} total processed)`);
      } catch (error) {
        console.error(`❌ Error processing ${file}:`, error.message);
      }
    }
  } catch (error) {
    console.error("❌ Error loading verses:", error.message);
  } finally {
    db.close((err) => {
      if (err) {
        console.error("❌ Error closing database:", err.message);
      } else {
        console.log("🔒 Database connection closed");
      }
    });
  }
}

// Initialize database with schema if needed
function initializeDatabase() {
  return new Promise((resolve, reject) => {
    db.serialize(() => {
      db.run(`CREATE TABLE IF NOT EXISTS verses (
        id TEXT PRIMARY KEY,
        religion TEXT,
        book TEXT,
        chapter INT,
        verse INT,
        text TEXT
      )`, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log("📋 Database schema initialized");
          resolve();
        }
      });
    });
  });
}

// Main execution
async function main() {
  try {
    await initializeDatabase();
    await loadVerses();
  } catch (error) {
    console.error("❌ Fatal error:", error.message);
    process.exit(1);
  }
}

main();